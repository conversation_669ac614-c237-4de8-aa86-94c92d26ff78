# Production Environment Variables
# Copy this file to .env and fill in the actual values

# Application
NODE_ENV=production
CLIENT_URL=https://your-domain.com

# Database
MONGO_ROOT_USERNAME=admin
MONGO_ROOT_PASSWORD=your_secure_mongo_root_password
MONGO_USERNAME=smartpoultry_user
MONGO_PASSWORD=your_secure_mongo_password
MONGO_DB_NAME=smartpoultry_prod

# Redis
REDIS_PASSWORD=your_secure_redis_password

# JWT
JWT_SECRET=your_super_secure_jwt_secret_key_at_least_32_characters_long

# AI Service
GEMINI_API_KEY=your_gemini_api_key_here

# Monitoring & Error Tracking
SENTRY_DSN=your_sentry_dsn_here

# Email Service (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# SSL Configuration
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/private.key

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_S3_BUCKET=your-backup-bucket
