apiVersion: apps/v1
kind: Deployment
metadata:
  name: logstash
  namespace: smartpoultry
  labels:
    app: logstash
spec:
  replicas: 1
  selector:
    matchLabels:
      app: logstash
  template:
    metadata:
      labels:
        app: logstash
    spec:
      containers:
      - name: logstash
        image: docker.elastic.co/logstash/logstash:8.11.0
        ports:
        - containerPort: 5044
        - containerPort: 9600
        volumeMounts:
        - name: logstash-config
          mountPath: /usr/share/logstash/pipeline
        - name: logstash-settings
          mountPath: /usr/share/logstash/config
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: logstash-config
        configMap:
          name: logstash-config
      - name: logstash-settings
        configMap:
          name: logstash-settings
---
apiVersion: v1
kind: Service
metadata:
  name: logstash-service
  namespace: smartpoultry
spec:
  selector:
    app: logstash
  ports:
  - port: 5044
    targetPort: 5044
    name: beats
  - port: 9600
    targetPort: 9600
    name: http
  type: ClusterIP
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: logstash-config
  namespace: smartpoultry
data:
  logstash.conf: |
    input {
      beats {
        port => 5044
      }
      http {
        port => 8080
      }
    }

    filter {
      if [fields][service] == "smartpoultry-backend" {
        grok {
          match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{GREEDYDATA:message}" }
        }
        date {
          match => [ "timestamp", "ISO8601" ]
        }
      }

      if [fields][service] == "smartpoultry-frontend" {
        grok {
          match => { "message" => "%{COMBINEDAPACHELOG}" }
        }
      }

      mutate {
        add_field => { "environment" => "production" }
        add_field => { "application" => "smartpoultry" }
      }
    }

    output {
      elasticsearch {
        hosts => ["elasticsearch-service:9200"]
        index => "smartpoultry-logs-%{+YYYY.MM.dd}"
      }
      stdout {
        codec => rubydebug
      }
    }
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: logstash-settings
  namespace: smartpoultry
data:
  logstash.yml: |
    http.host: "0.0.0.0"
    path.config: /usr/share/logstash/pipeline
    xpack.monitoring.enabled: false
