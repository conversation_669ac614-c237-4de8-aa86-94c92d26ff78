
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AuthProvider } from '../contexts/AuthContext';
import { FarmProvider } from '../contexts/FarmContext';
import { SocketProvider } from '../contexts/SocketContext';
import FarmSelector from '../components/FarmSelector';
import { BrowserRouter as Router } from 'react-router-dom';

describe('FarmSelector', () => {
  it('renders farm selection and creation options', async () => {
    render(
      <Router>
        <AuthProvider>
          <SocketProvider>
            <FarmProvider>
              <FarmSelector />
            </FarmProvider>
          </SocketProvider>
        </AuthProvider>
      </Router>
    );

    expect(screen.getByRole('heading', { name: 'Select Farm' })).toBeInTheDocument();
        expect(screen.getByRole('heading', { name: 'Create Farm' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Create Farm' })).toBeInTheDocument();
  });
});
