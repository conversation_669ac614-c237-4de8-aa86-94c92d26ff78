
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AuthProvider } from '../contexts/AuthContext';
import RegisterForm from '../components/RegisterForm';
import { BrowserRouter as Router } from 'react-router-dom';

describe('RegisterForm', () => {
  it('should display error message with existing email', async () => {
    render(
      <Router>
        <AuthProvider>
          <RegisterForm />
        </AuthProvider>
      </Router>
    );

    fireEvent.change(screen.getByLabelText(/name/i), {
      target: { value: 'Test User' },
    });
    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'password123' },
    });
    fireEvent.click(screen.getByRole('button', { name: /register/i }));

    await waitFor(() => {
      expect(screen.getByText('Registration Failed')).toBeInTheDocument();
    });
  });
});
