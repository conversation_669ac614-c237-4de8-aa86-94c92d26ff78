
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { AuthProvider } from '../contexts/AuthContext';
import { FarmProvider } from '../contexts/FarmContext';
import { SocketProvider } from '../contexts/SocketContext';
import ChickensPage from '../pages/ChickensPage';
import { BrowserRouter as Router } from 'react-router-dom';
import axios from 'axios';

// Mock axios to prevent actual API calls during tests


describe('ChickensPage', () => {
  it('renders chicken management page correctly', async () => {
    // Mock API responses
    axios.get.mockResolvedValueOnce({
      data: [
        { _id: '1', breed: 'Broiler', hatchDate: '2024-01-01T00:00:00.000Z', status: 'healthy' },
      ],
    });

    render(
      <Router>
        <AuthProvider value={{ user: { roles: ['admin'] }, token: 'fake-token' }}>
          <SocketProvider>
            <FarmProvider>
              <ChickensPage />
            </FarmProvider>
          </SocketProvider>
        </AuthProvider>
      </Router>
    );

    // Check if the main heading is rendered
    await waitFor(() => {
      expect(screen.getByRole('heading', { name: /chickens/i })).toBeInTheDocument();
    });

    // Check if the add new chicken button is rendered
    expect(await screen.findByRole('button', { name: /add_new_chicken/i })).toBeInTheDocument();

    // Check if the mocked chicken is rendered
    expect(screen.getByText('Broiler')).toBeInTheDocument();
  });
});
