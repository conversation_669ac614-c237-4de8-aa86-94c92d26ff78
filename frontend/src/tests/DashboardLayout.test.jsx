import React from 'react';
import { render, screen } from '@testing-library/react';
import { AuthProvider } from '../contexts/AuthContext';
import { FarmProvider } from '../contexts/FarmContext';
import { SocketProvider } from '../contexts/SocketContext';
import DashboardLayout from '../components/DashboardLayout';
import { BrowserRouter as Router } from 'react-router-dom';

describe('DashboardLayout', () => {
  it('renders the sidebar and navbar', () => {
    render(
      <Router>
        <AuthProvider>
          <SocketProvider>
            <FarmProvider>
              <DashboardLayout />
            </FarmProvider>
          </SocketProvider>
        </AuthProvider>
      </Router>
    );

    expect(screen.getByRole('navigation')).toBeInTheDocument();
    expect(screen.getByRole('banner')).toBeInTheDocument();
  });
});