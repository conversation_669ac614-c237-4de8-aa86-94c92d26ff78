import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../contexts/AuthContext';
import { useFarm } from '../contexts/FarmContext';
import DashboardLayout from '../components/DashboardLayout';
import ChickenList from '../components/ChickenList';
import ChickenForm from '../components/ChickenForm';
import axios from 'axios';

const ChickensPage = () => {
  const { t } = useTranslation();
  const { token, user } = useAuth();
  const { selectedFarm } = useFarm();
  const [chickens, setChickens] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editingChicken, setEditingChicken] = useState(null);

  const canManage = user && (user.roles.includes('admin') || user.roles.includes('manager'));

  useEffect(() => {
    const fetchChickens = async () => {
      if (!selectedFarm || !token) {
        setLoading(false);
        return;
      }
      try {
        const config = {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };
        const res = await axios.get(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/chickens`, config);
        setChickens(res.data);
      } catch (err) {
        console.error('Error fetching chickens:', err.response ? err.response.data : err.message);
        setError(err.response?.data?.message || t('failed_to_fetch_chickens'));
      } finally {
        setLoading(false);
      }
    };

    fetchChickens();
  }, [selectedFarm, token]);

  const handleAddChicken = async (chickenData) => {
    try {
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };
      const res = await axios.post(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/chickens`, chickenData, config);
      setChickens([...chickens, res.data.chicken]);
      setEditingChicken(null); // Clear form after adding
    } catch (err) {
      console.error('Error adding chicken:', err.response ? err.response.data : err.message);
      setError(err.response?.data?.message || t('failed_to_save_chicken'));
    }
  };

  const handleUpdateChicken = async (id, chickenData) => {
    try {
      const config = {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      };
      const res = await axios.put(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/chickens/${id}`, chickenData, config);
      setChickens(chickens.map((chicken) => (chicken._id === id ? res.data.chicken : chicken)));
      setEditingChicken(null); // Clear form after updating
    } catch (err) {
      console.error('Error updating chicken:', err.response ? err.response.data : err.message);
      setError(err.response?.data?.message || t('failed_to_save_chicken'));
    }
  };

  const handleDeleteChicken = async (id) => {
    if (window.confirm(t('confirm_delete_chicken'))) {
      try {
        const config = {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };
        await axios.delete(`${import.meta.env.VITE_BACKEND_URL}/api/v1/farms/${selectedFarm._id}/chickens/${id}`, config);
        setChickens(chickens.filter((chicken) => chicken._id !== id));
      } catch (err) {
        console.error('Error deleting chicken:', err.response ? err.response.data : err.message);
        setError(err.response?.data?.message || t('failed_to_delete_chicken'));
      }
    }
  };

  if (!selectedFarm) {
    return (
      <DashboardLayout>
        <h2 className="text-3xl font-bold text-gray-800 mb-6">{t('chickens')}</h2>
        <p className="text-gray-600">{t('no_farm_selected')}</p>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <h2 className="text-3xl font-bold text-gray-800 mb-6">{t('chickens')}</h2>
      {error && <p className="text-red-500 mb-4">{error}</p>}
      {canManage && (
        <div className="mb-6">
          <button
            onClick={() => setEditingChicken({})}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            data-testid="add-new-chicken-button"
          >
            {t('add_new_chicken')}
          </button>
        </div>
      )}

      {loading ? (
        <p>{t('loading')}...</p>
      ) : (
        <>
          {editingChicken && canManage && (
            <ChickenForm
              initialData={editingChicken}
              onSubmit={editingChicken._id ? (data) => handleUpdateChicken(editingChicken._id, data) : handleAddChicken}
              onCancel={() => setEditingChicken(null)}
            />
          )}
          <ChickenList chickens={chickens} onEdit={canManage ? setEditingChicken : null} onDelete={canManage ? handleDeleteChicken : null} />
        </>
      )}
    </DashboardLayout>
  );
};

export default ChickensPage;
