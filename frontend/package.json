{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"test": "vitest", "dev": "vite", "build": "vite build", "build:prod": "vite build --config vite.config.prod.js", "lint": "eslint .", "preview": "vite preview", "analyze": "vite-bundle-analyzer dist"}, "dependencies": {"autoprefixer": "^10.4.21", "axios": "^1.11.0", "chart.js": "^4.5.0", "i18next": "^25.3.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "postcss": "^8.5.6", "react": "^19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.0", "react-i18next": "^15.6.1", "react-router-dom": "^7.7.1", "recharts": "^3.1.0", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.11"}, "devDependencies": {"@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "babel-jest": "^30.0.5", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "jest-environment-jsdom-fourteen": "^1.0.1", "text-encoding": "^0.7.0", "vite": "^7.0.4", "vite-bundle-analyzer": "^0.11.0", "vite-plugin-static-copy": "^3.1.1", "vitest": "^3.2.4", "workbox-build": "^7.0.0", "workbox-window": "^7.0.0"}}