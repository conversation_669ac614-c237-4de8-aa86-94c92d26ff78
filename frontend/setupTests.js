import '@testing-library/jest-dom';
import 'text-encoding';
import axios from 'axios';

const i18nMock = {
  use: vi.fn(() => i18nMock),
  init: vi.fn(),
  changeLanguage: vi.fn(() => new Promise(() => {})),
  language: 'en',
};

vi.mock('i18next', () => ({
  __esModule: true,
  default: i18nMock,
}));

vi.mock('react-i18next', async () => {
  const originalModule = await vi.importActual('react-i18next');
  return {
    ...originalModule,
    useTranslation: () => ({
      t: (key) => {
        switch (key) {
          case 'chickens':
            return 'Chickens';
          case 'broiler':
            return 'Broiler';
          case 'add_new_chicken':
            return 'Add New Chicken';
          case 'healthy':
            return 'Healthy';
          case 'breed':
            return 'Breed';
          case 'hatch_date':
            return 'Hatch Date';
          case 'status':
            return 'Status';
          case 'actions':
            return 'Actions';
          case 'edit':
            return 'Edit';
          case 'delete':
            return 'Delete';
          case 'select_farm':
            return 'Select Farm';
          case 'create_new_farm':
            return 'Create New Farm';
          case 'create_farm':
            return 'Create Farm';
          case 'login_failed':
            return 'Login Failed';
          case 'registration_failed':
            return 'Registration Failed';
          case 'welcome':
            return 'Welcome to SmartPoultry!';
          case 'login':
            return 'Login';
          case 'register':
            return 'Register';
          case 'dashboard':
            return 'Dashboard';
          case 'logout':
            return 'Logout';
          case 'email':
            return 'Email';
          case 'password':
            return 'Password';
          case 'logging_in':
            return 'Logging In...';
          case 'name':
            return 'Name';
          case 'registering':
            return 'Registering...';
          case 'no_farms_found':
            return 'No farms found.';
          case 'create_farm_prompt':
            return 'Please create a new farm to get started.';
          case 'farm_name':
            return 'Farm Name';
          case 'location':
            return 'Location';
          case 'no_farm_selected':
            return 'No farm selected.';
          case 'loading':
            return 'Loading';
          default:
            return key;
        }
      },
      i18n: i18nMock,
    }),
  };
});

vi.mock('axios');

