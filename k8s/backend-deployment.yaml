apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend
  namespace: smartpoultry
  labels:
    app: backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: backend
  template:
    metadata:
      labels:
        app: backend
    spec:
      containers:
      - name: backend
        image: smartpoultry/backend:latest
        ports:
        - containerPort: 5000
        env:
        - name: NODE_ENV
          valueFrom:
            configMapKeyRef:
              name: smartpoultry-config
              key: NODE_ENV
        - name: PORT
          value: "5000"
        - name: MONGO_URI
          value: "mongodb://$(MONGO_USERNAME):$(MONGO_PASSWORD)@$(MONGODB_HOST):$(MONGODB_PORT)/$(MONGO_DB_NAME)?authSource=$(MONGO_DB_NAME)"
        - name: REDIS_URL
          value: "redis://:$(REDIS_PASSWORD)@$(REDIS_HOST):$(REDIS_PORT)"
        - name: CLIENT_URL
          valueFrom:
            configMapKeyRef:
              name: smartpoultry-config
              key: CLIENT_URL
        - name: MONGO_USERNAME
          valueFrom:
            secretKeyRef:
              name: smartpoultry-secrets
              key: MONGO_USERNAME
        - name: MONGO_PASSWORD
          valueFrom:
            secretKeyRef:
              name: smartpoultry-secrets
              key: MONGO_PASSWORD
        - name: MONGO_DB_NAME
          valueFrom:
            configMapKeyRef:
              name: smartpoultry-config
              key: MONGO_DB_NAME
        - name: MONGODB_HOST
          valueFrom:
            configMapKeyRef:
              name: smartpoultry-config
              key: MONGODB_HOST
        - name: MONGODB_PORT
          valueFrom:
            configMapKeyRef:
              name: smartpoultry-config
              key: MONGODB_PORT
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: smartpoultry-config
              key: REDIS_HOST
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: smartpoultry-config
              key: REDIS_PORT
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: smartpoultry-secrets
              key: REDIS_PASSWORD
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: smartpoultry-secrets
              key: JWT_SECRET
        - name: GEMINI_API_KEY
          valueFrom:
            secretKeyRef:
              name: smartpoultry-secrets
              key: GEMINI_API_KEY
        - name: SENTRY_DSN
          valueFrom:
            secretKeyRef:
              name: smartpoultry-secrets
              key: SENTRY_DSN
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 5000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: logs
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: backend-service
  namespace: smartpoultry
spec:
  selector:
    app: backend
  ports:
  - port: 5000
    targetPort: 5000
  type: ClusterIP
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-hpa
  namespace: smartpoultry
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
