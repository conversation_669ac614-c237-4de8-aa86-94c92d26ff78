apiVersion: batch/v1
kind: CronJob
metadata:
  name: mongodb-backup
  namespace: smartpoultry
spec:
  schedule: "0 2 * * *"  # Daily at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: mongodb-backup
            image: smartpoultry/backup:latest
            command:
            - /bin/bash
            - /scripts/backup-production.sh
            env:
            - name: MONGO_HOST
              value: "mongodb-service"
            - name: MONGO_PORT
              value: "27017"
            - name: MON<PERSON>O_DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: smartpoultry-config
                  key: MONGO_DB_NAME
            - name: MONGO_USERNAME
              valueFrom:
                secretKeyRef:
                  name: smartpoultry-secrets
                  key: MONGO_USERNAME
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: smartpoultry-secrets
                  key: MONGO_PASSWORD
            - name: AWS_ACCESS_KEY_ID
              valueFrom:
                secretKeyRef:
                  name: aws-secrets
                  key: access-key-id
            - name: AWS_SECRET_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-secrets
                  key: secret-access-key
            - name: AWS_S3_BUCKET
              valueFrom:
                configMapKeyRef:
                  name: backup-config
                  key: s3-bucket
            - name: BACKUP_RETENTION_DAYS
              valueFrom:
                configMapKeyRef:
                  name: backup-config
                  key: retention-days
            volumeMounts:
            - name: backup-scripts
              mountPath: /scripts
            - name: backup-logs
              mountPath: /var/log
          volumes:
          - name: backup-scripts
            configMap:
              name: backup-scripts
              defaultMode: 0755
          - name: backup-logs
            emptyDir: {}
          restartPolicy: OnFailure
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 1
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: backup-config
  namespace: smartpoultry
data:
  s3-bucket: "smartpoultry-backups"
  retention-days: "30"
---
apiVersion: v1
kind: Secret
metadata:
  name: aws-secrets
  namespace: smartpoultry
type: Opaque
data:
  # Base64 encoded AWS credentials
  access-key-id: eW91cl9hd3NfYWNjZXNzX2tleQ==  # your_aws_access_key
  secret-access-key: eW91cl9hd3Nfc2VjcmV0X2tleQ==  # your_aws_secret_key
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: backup-scripts
  namespace: smartpoultry
data:
  backup-production.sh: |
    #!/bin/bash
    # The backup script content would be embedded here
    # For brevity, this is a placeholder - in production, 
    # you would include the full script content
