apiVersion: v1
kind: Secret
metadata:
  name: smartpoultry-secrets
  namespace: smartpoultry
type: Opaque
data:
  # Base64 encoded values - replace with actual encoded secrets
  # Use: echo -n "your_secret" | base64
  MONGO_ROOT_USERNAME: YWRtaW4=  # admin
  MONGO_ROOT_PASSWORD: Y2hhbmdlX3RoaXNfcGFzc3dvcmQ=  # change_this_password
  MONGO_USERNAME: c21hcnRwb3VsdHJ5X3VzZXI=  # smartpoultry_user
  MONGO_PASSWORD: Y2hhbmdlX3RoaXNfcGFzc3dvcmQ=  # change_this_password
  REDIS_PASSWORD: Y2hhbmdlX3RoaXNfcGFzc3dvcmQ=  # change_this_password
  JWT_SECRET: eW91cl9zdXBlcl9zZWN1cmVfand0X3NlY3JldF9rZXlfYXRfbGVhc3RfMzJfY2hhcmFjdGVyc19sb25n  # your_super_secure_jwt_secret_key_at_least_32_characters_long
  GEMINI_API_KEY: eW91cl9nZW1pbmlfYXBpX2tleV9oZXJl  # your_gemini_api_key_here
  SENTRY_DSN: eW91cl9zZW50cnlfZHNuX2hlcmU=  # your_sentry_dsn_here
