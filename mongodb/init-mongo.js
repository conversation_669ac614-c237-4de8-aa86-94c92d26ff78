// MongoDB initialization script for production
db = db.getSiblingDB(process.env.MONGO_DB_NAME || 'smartpoultry_prod');

// Create application user
db.createUser({
  user: process.env.MONGO_USERNAME || 'smartpoultry_user',
  pwd: process.env.MONGO_PASSWORD || 'change_this_password',
  roles: [
    {
      role: 'readWrite',
      db: process.env.MONGO_DB_NAME || 'smartpoultry_prod'
    }
  ]
});

// Create indexes for better performance
db.users.createIndex({ email: 1 }, { unique: true });
db.farms.createIndex({ owner: 1 });
db.farms.createIndex({ name: 1, owner: 1 });

db.chickens.createIndex({ farm: 1 });
db.chickens.createIndex({ farm: 1, status: 1 });
db.chickens.createIndex({ farm: 1, breed: 1 });

db.eggproductions.createIndex({ farm: 1 });
db.eggproductions.createIndex({ farm: 1, date: -1 });

db.feedrecords.createIndex({ farm: 1 });
db.feedrecords.createIndex({ farm: 1, date: -1 });

db.healthrecords.createIndex({ farm: 1 });
db.healthrecords.createIndex({ farm: 1, date: -1 });
db.healthrecords.createIndex({ farm: 1, chickenId: 1 });

db.financialrecords.createIndex({ farm: 1 });
db.financialrecords.createIndex({ farm: 1, date: -1 });
db.financialrecords.createIndex({ farm: 1, type: 1 });

db.suppliers.createIndex({ farm: 1 });
db.customers.createIndex({ farm: 1 });

db.aiinsightlogs.createIndex({ farm: 1 });
db.aiinsightlogs.createIndex({ farm: 1, type: 1 });
db.aiinsightlogs.createIndex({ farm: 1, createdAt: -1 });

print('Database initialization completed successfully!');
