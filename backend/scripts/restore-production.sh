#!/bin/bash

# Production MongoDB Restore Script with AWS S3 Integration

set -e  # Exit on any error

# Configuration
MONGO_HOST="${MONGO_HOST:-mongodb-service}"
MONGO_PORT="${MONGO_PORT:-27017}"
MONGO_DB="${MONGO_DB_NAME:-smartpoultry_prod}"
MONGO_USER="${MONGO_USERNAME}"
MONGO_PASS="${MONGO_PASSWORD}"
RESTORE_DIR="/tmp/mongo_restore"
S3_BUCKET="${AWS_S3_BUCKET}"
S3_PREFIX="mongodb-backups"

# Logging
LOG_FILE="/var/log/mongodb-restore.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

# Function to show usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -f, --file BACKUP_FILE    Local backup file to restore"
    echo "  -s, --s3 S3_KEY          S3 backup key to restore"
    echo "  -l, --list               List available S3 backups"
    echo "  -d, --drop               Drop existing database before restore"
    echo "  -h, --help               Show this help message"
    exit 1
}

# Function to list S3 backups
list_s3_backups() {
    if [ -z "$S3_BUCKET" ]; then
        echo "Error: S3_BUCKET not configured"
        exit 1
    fi
    
    echo "Available backups in S3:"
    aws s3 ls "s3://${S3_BUCKET}/${S3_PREFIX}/" | awk '{print $4}' | sort -r
}

# Function to download from S3
download_from_s3() {
    local s3_key="$1"
    local local_file="${RESTORE_DIR}/$(basename "$s3_key")"
    
    echo "$(date): Downloading backup from S3: $s3_key"
    aws s3 cp "s3://${S3_BUCKET}/${S3_PREFIX}/${s3_key}" "$local_file"
    
    if [ $? -eq 0 ]; then
        echo "$(date): Download successful: $local_file"
        echo "$local_file"
    else
        echo "$(date): Failed to download from S3"
        exit 1
    fi
}

# Function to extract backup
extract_backup() {
    local backup_file="$1"
    local extract_dir="${RESTORE_DIR}/extracted"
    
    echo "$(date): Extracting backup: $backup_file"
    mkdir -p "$extract_dir"
    
    if [[ "$backup_file" == *.tar.gz ]]; then
        tar -xzf "$backup_file" -C "$extract_dir"
    elif [[ "$backup_file" == *.zip ]]; then
        unzip "$backup_file" -d "$extract_dir"
    else
        echo "$(date): Unsupported backup format"
        exit 1
    fi
    
    # Find the database directory
    local db_dir=$(find "$extract_dir" -name "$MONGO_DB" -type d | head -1)
    if [ -z "$db_dir" ]; then
        echo "$(date): Database directory not found in backup"
        exit 1
    fi
    
    echo "$db_dir"
}

# Function to perform restore
perform_restore() {
    local restore_path="$1"
    local drop_db="$2"
    
    echo "$(date): Starting MongoDB restore for database: ${MONGO_DB}"
    echo "$(date): Restore path: $restore_path"
    
    # Build mongorestore command
    local restore_cmd="mongorestore --host ${MONGO_HOST}:${MONGO_PORT}"
    
    if [ -n "$MONGO_USER" ] && [ -n "$MONGO_PASS" ]; then
        restore_cmd="$restore_cmd --username ${MONGO_USER} --password ${MONGO_PASS} --authenticationDatabase ${MONGO_DB}"
    fi
    
    if [ "$drop_db" = "true" ]; then
        restore_cmd="$restore_cmd --drop"
        echo "$(date): WARNING: Existing database will be dropped!"
        read -p "Are you sure you want to continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "$(date): Restore cancelled by user"
            exit 1
        fi
    fi
    
    restore_cmd="$restore_cmd --gzip --dir $restore_path"
    
    echo "$(date): Executing restore command..."
    eval "$restore_cmd"
    
    if [ $? -eq 0 ]; then
        echo "$(date): MongoDB restore completed successfully"
    else
        echo "$(date): MongoDB restore failed!"
        exit 1
    fi
}

# Function to cleanup
cleanup() {
    echo "$(date): Cleaning up temporary files..."
    rm -rf "$RESTORE_DIR"
}

# Trap cleanup on exit
trap cleanup EXIT

# Parse command line arguments
BACKUP_FILE=""
S3_KEY=""
DROP_DB="false"
LIST_BACKUPS="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--file)
            BACKUP_FILE="$2"
            shift 2
            ;;
        -s|--s3)
            S3_KEY="$2"
            shift 2
            ;;
        -l|--list)
            LIST_BACKUPS="true"
            shift
            ;;
        -d|--drop)
            DROP_DB="true"
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo "Unknown option: $1"
            usage
            ;;
    esac
done

# Create restore directory
mkdir -p "$RESTORE_DIR"

# Handle list backups
if [ "$LIST_BACKUPS" = "true" ]; then
    list_s3_backups
    exit 0
fi

# Validate input
if [ -z "$BACKUP_FILE" ] && [ -z "$S3_KEY" ]; then
    echo "Error: Either --file or --s3 must be specified"
    usage
fi

if [ -n "$BACKUP_FILE" ] && [ -n "$S3_KEY" ]; then
    echo "Error: Cannot specify both --file and --s3"
    usage
fi

# Process backup
if [ -n "$S3_KEY" ]; then
    BACKUP_FILE=$(download_from_s3 "$S3_KEY")
fi

if [ ! -f "$BACKUP_FILE" ]; then
    echo "Error: Backup file not found: $BACKUP_FILE"
    exit 1
fi

# Extract and restore
RESTORE_PATH=$(extract_backup "$BACKUP_FILE")
perform_restore "$RESTORE_PATH" "$DROP_DB"

echo "$(date): Restore process completed successfully"

# Send notification (if configured)
if [ -n "$WEBHOOK_URL" ]; then
    curl -X POST "$WEBHOOK_URL" \
        -H "Content-Type: application/json" \
        -d "{\"text\":\"MongoDB restore completed successfully for ${MONGO_DB} at $(date)\"}"
fi
