#!/bin/bash

# Production MongoDB Backup Script with AWS S3 Integration
# This script creates backups and uploads them to AWS S3

set -e  # Exit on any error

# Configuration
MONGO_HOST="${MONGO_HOST:-mongodb-service}"
MONGO_PORT="${MONGO_PORT:-27017}"
MONGO_DB="${MONGO_DB_NAME:-smartpoultry_prod}"
MONGO_USER="${MONGO_USERNAME}"
MONGO_PASS="${MONGO_PASSWORD}"
BACKUP_DIR="/tmp/mongo_backup"
DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="${MONGO_DB}_${DATE}"
BACKUP_PATH="${BACKUP_DIR}/${BACKUP_NAME}"
RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"

# AWS S3 Configuration
S3_BUCKET="${AWS_S3_BUCKET}"
S3_PREFIX="mongodb-backups"

# Logging
LOG_FILE="/var/log/mongodb-backup.log"
exec 1> >(tee -a "$LOG_FILE")
exec 2>&1

echo "$(date): Starting MongoDB backup for database: ${MONGO_DB}"

# Create backup directory
mkdir -p "${BACKUP_DIR}"

# Perform backup
echo "$(date): Creating backup..."
if [ -n "$MONGO_USER" ] && [ -n "$MONGO_PASS" ]; then
    mongodump \
        --host "${MONGO_HOST}:${MONGO_PORT}" \
        --db "${MONGO_DB}" \
        --username "${MONGO_USER}" \
        --password "${MONGO_PASS}" \
        --authenticationDatabase "${MONGO_DB}" \
        --out "${BACKUP_PATH}" \
        --gzip
else
    mongodump \
        --host "${MONGO_HOST}:${MONGO_PORT}" \
        --db "${MONGO_DB}" \
        --out "${BACKUP_PATH}" \
        --gzip
fi

if [ $? -eq 0 ]; then
    echo "$(date): MongoDB backup successful: ${BACKUP_PATH}"
else
    echo "$(date): MongoDB backup failed!"
    exit 1
fi

# Create compressed archive
echo "$(date): Creating compressed archive..."
cd "${BACKUP_DIR}"
tar -czf "${BACKUP_NAME}.tar.gz" "${BACKUP_NAME}"

if [ $? -eq 0 ]; then
    echo "$(date): Archive created successfully: ${BACKUP_NAME}.tar.gz"
    # Remove uncompressed backup
    rm -rf "${BACKUP_NAME}"
else
    echo "$(date): Failed to create archive!"
    exit 1
fi

# Upload to S3 if configured
if [ -n "$S3_BUCKET" ]; then
    echo "$(date): Uploading backup to S3..."
    aws s3 cp "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz" "s3://${S3_BUCKET}/${S3_PREFIX}/${BACKUP_NAME}.tar.gz"
    
    if [ $? -eq 0 ]; then
        echo "$(date): Backup uploaded to S3 successfully"
        # Remove local backup after successful upload
        rm -f "${BACKUP_DIR}/${BACKUP_NAME}.tar.gz"
    else
        echo "$(date): Failed to upload backup to S3"
        exit 1
    fi
    
    # Clean up old backups from S3
    echo "$(date): Cleaning up old backups from S3..."
    CUTOFF_DATE=$(date -d "${RETENTION_DAYS} days ago" +%Y%m%d)
    aws s3 ls "s3://${S3_BUCKET}/${S3_PREFIX}/" | while read -r line; do
        BACKUP_DATE=$(echo "$line" | awk '{print $4}' | grep -o '[0-9]\{8\}' | head -1)
        if [ -n "$BACKUP_DATE" ] && [ "$BACKUP_DATE" -lt "$CUTOFF_DATE" ]; then
            BACKUP_FILE=$(echo "$line" | awk '{print $4}')
            echo "$(date): Deleting old backup: $BACKUP_FILE"
            aws s3 rm "s3://${S3_BUCKET}/${S3_PREFIX}/${BACKUP_FILE}"
        fi
    done
else
    echo "$(date): S3 not configured, keeping local backup"
    
    # Clean up old local backups
    echo "$(date): Cleaning up old local backups..."
    find "${BACKUP_DIR}" -name "*.tar.gz" -mtime +${RETENTION_DAYS} -delete
fi

echo "$(date): Backup process completed successfully"

# Send notification (if configured)
if [ -n "$WEBHOOK_URL" ]; then
    curl -X POST "$WEBHOOK_URL" \
        -H "Content-Type: application/json" \
        -d "{\"text\":\"MongoDB backup completed successfully for ${MONGO_DB} at $(date)\"}"
fi
