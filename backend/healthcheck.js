const http = require('http');
const mongoose = require('mongoose');

const options = {
  hostname: 'localhost',
  port: process.env.PORT || 5000,
  path: '/health',
  method: 'GET',
  timeout: 3000
};

const healthCheck = http.request(options, (res) => {
  console.log(`Health check status: ${res.statusCode}`);
  
  if (res.statusCode === 200) {
    // Check database connection
    if (mongoose.connection.readyState === 1) {
      console.log('Health check passed - API and Database are healthy');
      process.exit(0);
    } else {
      console.log('Health check failed - Database connection issue');
      process.exit(1);
    }
  } else {
    console.log('Health check failed - API not responding correctly');
    process.exit(1);
  }
});

healthCheck.on('error', (err) => {
  console.log('Health check failed - API not reachable:', err.message);
  process.exit(1);
});

healthCheck.on('timeout', () => {
  console.log('Health check failed - Request timeout');
  healthCheck.destroy();
  process.exit(1);
});

healthCheck.end();
