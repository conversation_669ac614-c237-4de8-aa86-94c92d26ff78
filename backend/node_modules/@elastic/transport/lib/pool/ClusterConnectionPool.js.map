{"version": 3, "file": "ClusterConnectionPool.js", "sourceRoot": "", "sources": ["../../src/pool/ClusterConnectionPool.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,mFAI6B;AAC7B,sEAAgC;AAChC,0DAAyB;AACzB,8CAA6E;AAG7E,MAAM,KAAK,GAAG,IAAA,eAAK,EAAC,eAAe,CAAC,CAAA;AAiBpC,MAAqB,qBAAsB,SAAQ,4BAAkB;IAanE,YAAa,IAA2B;;QACtC,KAAK,CAAC,IAAI,CAAC,CAAA;QAbb;;;;;WAAc;QACd;;;;;WAAwB;QACxB;;;;;WAA8B;QAC9B;;;;;WAAmB;QACnB;;;;;WAAyB;QAWvB,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA;QACd,+BAA+B;QAC/B,IAAI,CAAC,gBAAgB,GAAG,IAAI,GAAG,EAAE,CAAA;QACjC,6CAA6C;QAC7C,+BAA+B;QAC/B,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAA;QAC/B,IAAI,CAAC,WAAW,GAAG,MAAA,IAAI,CAAC,WAAW,mCAAI,IAAI,CAAA;QAE3C,MAAM,iBAAiB,GAAG,MAAA,IAAI,CAAC,iBAAiB,mCAAI,MAAM,CAAA;QAC1D,IAAI,CAAC,iBAAiB,GAAG,qBAAqB,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,CAAA;QACrF,IAAA,qBAAM,EACJ,IAAI,CAAC,iBAAiB,IAAI,IAAI,EAC9B,mCAAmC,iBAAiB,GAAG,CACxD,CAAA;IACH,CAAC;IAED;;;;;;OAMG;IACH,SAAS,CAAE,UAAsB;QAC/B,MAAM,EAAE,EAAE,EAAE,GAAG,UAAU,CAAA;QACzB,KAAK,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAA;QAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;QACnC,IAAI,KAAK,GAAG,CAAC,CAAC;YAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;QAC1C,UAAU,CAAC,MAAM,GAAG,2BAAc,CAAC,QAAQ,CAAC,KAAK,CAAA;QACjD,UAAU,CAAC,SAAS,GAAG,CAAC,CAAA;QACxB,UAAU,CAAC,gBAAgB,GAAG,CAAC,CAAA;QAC/B,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;OAMG;IACH,QAAQ,CAAE,UAAsB;QAC9B,MAAM,EAAE,EAAE,EAAE,GAAG,UAAU,CAAA;QACzB,KAAK,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAA;QAC7C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;YAC5B,uDAAuD;YACvD,0DAA0D;YAC1D,+DAA+D;YAC/D,uDAAuD;YACvD,sBAAsB;YACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;gBACnC,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;oBAClC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;oBAClB,MAAK;gBACP,CAAC;YACH,CAAC;QACH,CAAC;QACD,UAAU,CAAC,MAAM,GAAG,2BAAc,CAAC,QAAQ,CAAC,IAAI,CAAA;QAChD,UAAU,CAAC,SAAS,EAAE,CAAA;QACtB,4BAA4B;QAC5B,uEAAuE;QACvE,UAAU,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CACzE,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI,CAAC,sBAAsB,CAAC,CACnE,CAAA;QAED,wCAAwC;QACxC,gCAAgC;QAChC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACtB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAe,CAAA;YAClE,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAe,CAAA;YAClE,OAAO,KAAK,CAAC,gBAAgB,GAAG,KAAK,CAAC,gBAAgB,CAAA;QACxD,CAAC,CAAC,CAAA;QAEF,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;OAKG;IACH,SAAS,CAAE,IAAsB;QAC/B,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC3D,KAAK,CAAC,sBAAsB,CAAC,CAAA;YAC7B,OAAM;QACR,CAAC;QAED,kEAAkE;QAClE,uEAAuE;QACvE,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAe,CAAA;QAClF,IAAI,IAAI,CAAC,GAAG,GAAG,UAAU,CAAC,gBAAgB,EAAE,CAAC;YAC3C,KAAK,CAAC,sBAAsB,CAAC,CAAA;YAC7B,OAAM;QACR,CAAC;QAED,MAAM,EAAE,EAAE,EAAE,GAAG,UAAU,CAAA;QAEzB,gBAAgB;QAChB,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE,CAAC;YACjC,UAAU,CAAC,OAAO,CAChB,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,EAC7B,EAAE,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,SAAS,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,CACjG;iBACE,IAAI,CAAC,CAAC,EAAE,UAAU,EAAE,EAAE,EAAE;gBACvB,IAAI,OAAO,GAAG,IAAI,CAAA;gBAClB,IAAI,UAAU,KAAK,GAAG,IAAI,UAAU,KAAK,GAAG,IAAI,UAAU,KAAK,GAAG,EAAE,CAAC;oBACnE,KAAK,CAAC,0BAA0B,EAAE,iBAAiB,CAAC,CAAA;oBACpD,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;oBACzB,OAAO,GAAG,KAAK,CAAA;gBACjB,CAAC;qBAAM,CAAC;oBACN,KAAK,CAAC,0BAA0B,EAAE,gBAAgB,CAAC,CAAA;oBACnD,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;gBAC5B,CAAC;gBACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE;oBACtC,QAAQ,EAAE,MAAM;oBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,OAAO,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE;oBAC/B,OAAO;oBACP,UAAU;iBACX,CAAC,CAAA;YACJ,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,GAAU,EAAE,EAAE;gBACpB,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;gBACzB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE;oBACrC,QAAQ,EAAE,MAAM;oBAChB,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,OAAO,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE;oBAC/B,OAAO,EAAE,KAAK;oBACd,UAAU;iBACX,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;YACN,sBAAsB;QACtB,CAAC;aAAM,CAAC;YACN,KAAK,CAAC,sDAAsD,EAAE,GAAG,CAAC,CAAA;YAClE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAA;YAC1C,UAAU,CAAC,MAAM,GAAG,2BAAc,CAAC,QAAQ,CAAC,KAAK,CAAA;YACjD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE;gBACtC,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,OAAO,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE;gBAC/B,OAAO,EAAE,IAAI;gBACb,UAAU;aACX,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;IAED;;;;;;;;;OASG;IACH,aAAa,CAAE,IAA0B;QACvC,MAAM,MAAM,GAAiB,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,sCAAiB,CAAA;QAClF,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAe,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAElF,IAAI,CAAC,SAAS,CAAC;YACb,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC,CAAA;QAEF,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAA;QAEzD,2BAA2B;QAC3B,MAAM,WAAW,GAAG,EAAE,CAAA;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;YACnC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;YACtC,IAAI,kBAAkB,IAAI,UAAU,CAAC,MAAM,KAAK,2BAAc,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;gBAC9E,IAAI,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;oBACvB,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;gBAC9B,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,CAAA;QAEzC,OAAO,QAAQ,CAAC,WAAW,CAAC,CAAA;IAC9B,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,KAAK;QACT,MAAM,KAAK,CAAC,KAAK,EAAE,CAAA;QACnB,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA;IAChB,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAE,WAAkD;QACxD,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;QACzB,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA;QACd,OAAO,IAAI,CAAA;IACb,CAAC;;AAvNM;;;;WAAsB;QAC3B,IAAI,EAAE,CAAC;QACP,IAAI,EAAE,CAAC;QACP,UAAU,EAAE,CAAC;KACd;EAJyB,CAIzB;kBAXkB,qBAAqB"}