{"version": 3, "file": "dedupe.js", "sources": ["../../../src/dedupe.ts"], "sourcesContent": ["import { convertIntegrationFnToClass, defineIntegration } from '@sentry/core';\nimport type { Event, Exception, Integration, IntegrationClass, IntegrationFn, StackFrame } from '@sentry/types';\nimport { logger } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from './debug-build';\n\nconst INTEGRATION_NAME = 'Dedupe';\n\nconst _dedupeIntegration = (() => {\n  let previousEvent: Event | undefined;\n\n  return {\n    name: INTEGRATION_NAME,\n    // TODO v8: Remove this\n    setupOnce() {}, // eslint-disable-line @typescript-eslint/no-empty-function\n    processEvent(currentEvent) {\n      // We want to ignore any non-error type events, e.g. transactions or replays\n      // These should never be deduped, and also not be compared against as _previousEvent.\n      if (currentEvent.type) {\n        return currentEvent;\n      }\n\n      // Juuust in case something goes wrong\n      try {\n        if (_shouldDropEvent(currentEvent, previousEvent)) {\n          DEBUG_BUILD && logger.warn('Event dropped due to being a duplicate of previously captured event.');\n          return null;\n        }\n      } catch (_oO) {} // eslint-disable-line no-empty\n\n      return (previousEvent = currentEvent);\n    },\n  };\n}) satisfies IntegrationFn;\n\nexport const dedupeIntegration = defineIntegration(_dedupeIntegration);\n\n/**\n * Deduplication filter.\n * @deprecated Use `dedupeIntegration()` instead.\n */\n// eslint-disable-next-line deprecation/deprecation\nexport const Dedupe = convertIntegrationFnToClass(INTEGRATION_NAME, dedupeIntegration) as IntegrationClass<\n  Integration & { processEvent: (event: Event) => Event }\n>;\n\n/** only exported for tests. */\nexport function _shouldDropEvent(currentEvent: Event, previousEvent?: Event): boolean {\n  if (!previousEvent) {\n    return false;\n  }\n\n  if (_isSameMessageEvent(currentEvent, previousEvent)) {\n    return true;\n  }\n\n  if (_isSameExceptionEvent(currentEvent, previousEvent)) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction _isSameMessageEvent(currentEvent: Event, previousEvent: Event): boolean {\n  const currentMessage = currentEvent.message;\n  const previousMessage = previousEvent.message;\n\n  // If neither event has a message property, they were both exceptions, so bail out\n  if (!currentMessage && !previousMessage) {\n    return false;\n  }\n\n  // If only one event has a stacktrace, but not the other one, they are not the same\n  if ((currentMessage && !previousMessage) || (!currentMessage && previousMessage)) {\n    return false;\n  }\n\n  if (currentMessage !== previousMessage) {\n    return false;\n  }\n\n  if (!_isSameFingerprint(currentEvent, previousEvent)) {\n    return false;\n  }\n\n  if (!_isSameStacktrace(currentEvent, previousEvent)) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction _isSameExceptionEvent(currentEvent: Event, previousEvent: Event): boolean {\n  const previousException = _getExceptionFromEvent(previousEvent);\n  const currentException = _getExceptionFromEvent(currentEvent);\n\n  if (!previousException || !currentException) {\n    return false;\n  }\n\n  if (previousException.type !== currentException.type || previousException.value !== currentException.value) {\n    return false;\n  }\n\n  if (!_isSameFingerprint(currentEvent, previousEvent)) {\n    return false;\n  }\n\n  if (!_isSameStacktrace(currentEvent, previousEvent)) {\n    return false;\n  }\n\n  return true;\n}\n\nfunction _isSameStacktrace(currentEvent: Event, previousEvent: Event): boolean {\n  let currentFrames = _getFramesFromEvent(currentEvent);\n  let previousFrames = _getFramesFromEvent(previousEvent);\n\n  // If neither event has a stacktrace, they are assumed to be the same\n  if (!currentFrames && !previousFrames) {\n    return true;\n  }\n\n  // If only one event has a stacktrace, but not the other one, they are not the same\n  if ((currentFrames && !previousFrames) || (!currentFrames && previousFrames)) {\n    return false;\n  }\n\n  currentFrames = currentFrames as StackFrame[];\n  previousFrames = previousFrames as StackFrame[];\n\n  // If number of frames differ, they are not the same\n  if (previousFrames.length !== currentFrames.length) {\n    return false;\n  }\n\n  // Otherwise, compare the two\n  for (let i = 0; i < previousFrames.length; i++) {\n    const frameA = previousFrames[i];\n    const frameB = currentFrames[i];\n\n    if (\n      frameA.filename !== frameB.filename ||\n      frameA.lineno !== frameB.lineno ||\n      frameA.colno !== frameB.colno ||\n      frameA.function !== frameB.function\n    ) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction _isSameFingerprint(currentEvent: Event, previousEvent: Event): boolean {\n  let currentFingerprint = currentEvent.fingerprint;\n  let previousFingerprint = previousEvent.fingerprint;\n\n  // If neither event has a fingerprint, they are assumed to be the same\n  if (!currentFingerprint && !previousFingerprint) {\n    return true;\n  }\n\n  // If only one event has a fingerprint, but not the other one, they are not the same\n  if ((currentFingerprint && !previousFingerprint) || (!currentFingerprint && previousFingerprint)) {\n    return false;\n  }\n\n  currentFingerprint = currentFingerprint as string[];\n  previousFingerprint = previousFingerprint as string[];\n\n  // Otherwise, compare the two\n  try {\n    return !!(currentFingerprint.join('') === previousFingerprint.join(''));\n  } catch (_oO) {\n    return false;\n  }\n}\n\nfunction _getExceptionFromEvent(event: Event): Exception | undefined {\n  return event.exception && event.exception.values && event.exception.values[0];\n}\n\nfunction _getFramesFromEvent(event: Event): StackFrame[] | undefined {\n  const exception = event.exception;\n\n  if (exception) {\n    try {\n      // @ts-expect-error Object could be undefined\n      return exception.values[0].stacktrace.frames;\n    } catch (_oO) {\n      return undefined;\n    }\n  }\n  return undefined;\n}\n"], "names": ["DEBUG_BUILD", "logger", "defineIntegration", "convertIntegrationFnToClass"], "mappings": ";;;;;;AAMA,MAAM,gBAAA,GAAmB,QAAQ,CAAA;AACjC;AACA,MAAM,kBAAmB,IAAG,MAAM;AAClC,EAAE,IAAI,aAAa,CAAA;AACnB;AACA,EAAE,OAAO;AACT,IAAI,IAAI,EAAE,gBAAgB;AAC1B;AACA,IAAI,SAAS,GAAG,EAAE;AAClB,IAAI,YAAY,CAAC,YAAY,EAAE;AAC/B;AACA;AACA,MAAM,IAAI,YAAY,CAAC,IAAI,EAAE;AAC7B,QAAQ,OAAO,YAAY,CAAA;AAC3B,OAAM;AACN;AACA;AACA,MAAM,IAAI;AACV,QAAQ,IAAI,gBAAgB,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE;AAC3D,UAAUA,0BAAeC,YAAM,CAAC,IAAI,CAAC,sEAAsE,CAAC,CAAA;AAC5G,UAAU,OAAO,IAAI,CAAA;AACrB,SAAQ;AACR,OAAQ,CAAA,OAAO,GAAG,EAAE,EAAC;AACrB;AACA,MAAM,QAAQ,aAAc,GAAE,YAAY,EAAC;AAC3C,KAAK;AACL,GAAG,CAAA;AACH,CAAC,CAAE,EAAA;AACH;MACa,iBAAkB,GAAEC,sBAAiB,CAAC,kBAAkB,EAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,SAASC,gCAA2B,CAAC,gBAAgB,EAAE,iBAAiB,CAAE;;CAEvF;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,YAAY,EAAS,aAAa,EAAmB;AACtF,EAAE,IAAI,CAAC,aAAa,EAAE;AACtB,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,IAAI,mBAAmB,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE;AACxD,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA,EAAE,IAAI,qBAAqB,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE;AAC1D,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA,EAAE,OAAO,KAAK,CAAA;AACd,CAAA;AACA;AACA,SAAS,mBAAmB,CAAC,YAAY,EAAS,aAAa,EAAkB;AACjF,EAAE,MAAM,cAAA,GAAiB,YAAY,CAAC,OAAO,CAAA;AAC7C,EAAE,MAAM,eAAA,GAAkB,aAAa,CAAC,OAAO,CAAA;AAC/C;AACA;AACA,EAAE,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE;AAC3C,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA;AACA,EAAE,IAAI,CAAC,cAAA,IAAkB,CAAC,eAAe,MAAM,CAAC,cAAA,IAAkB,eAAe,CAAC,EAAE;AACpF,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,IAAI,cAAe,KAAI,eAAe,EAAE;AAC1C,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE;AACxD,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE;AACvD,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,OAAO,IAAI,CAAA;AACb,CAAA;AACA;AACA,SAAS,qBAAqB,CAAC,YAAY,EAAS,aAAa,EAAkB;AACnF,EAAE,MAAM,iBAAkB,GAAE,sBAAsB,CAAC,aAAa,CAAC,CAAA;AACjE,EAAE,MAAM,gBAAiB,GAAE,sBAAsB,CAAC,YAAY,CAAC,CAAA;AAC/D;AACA,EAAE,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,EAAE;AAC/C,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,IAAI,iBAAiB,CAAC,IAAA,KAAS,gBAAgB,CAAC,IAAK,IAAG,iBAAiB,CAAC,KAAA,KAAU,gBAAgB,CAAC,KAAK,EAAE;AAC9G,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE;AACxD,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,aAAa,CAAC,EAAE;AACvD,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,OAAO,IAAI,CAAA;AACb,CAAA;AACA;AACA,SAAS,iBAAiB,CAAC,YAAY,EAAS,aAAa,EAAkB;AAC/E,EAAE,IAAI,aAAc,GAAE,mBAAmB,CAAC,YAAY,CAAC,CAAA;AACvD,EAAE,IAAI,cAAe,GAAE,mBAAmB,CAAC,aAAa,CAAC,CAAA;AACzD;AACA;AACA,EAAE,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE;AACzC,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA,EAAE,IAAI,CAAC,aAAA,IAAiB,CAAC,cAAc,MAAM,CAAC,aAAA,IAAiB,cAAc,CAAC,EAAE;AAChF,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,aAAA,GAAgB,aAAc,EAAA;AAChC,EAAE,cAAA,GAAiB,cAAe,EAAA;AAClC;AACA;AACA,EAAE,IAAI,cAAc,CAAC,WAAW,aAAa,CAAC,MAAM,EAAE;AACtD,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA;AACA,EAAE,KAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAE,GAAE,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClD,IAAI,MAAM,MAAO,GAAE,cAAc,CAAC,CAAC,CAAC,CAAA;AACpC,IAAI,MAAM,MAAO,GAAE,aAAa,CAAC,CAAC,CAAC,CAAA;AACnC;AACA,IAAI;AACJ,MAAM,MAAM,CAAC,QAAA,KAAa,MAAM,CAAC,QAAS;AAC1C,MAAM,MAAM,CAAC,MAAA,KAAW,MAAM,CAAC,MAAO;AACtC,MAAM,MAAM,CAAC,KAAA,KAAU,MAAM,CAAC,KAAM;AACpC,MAAM,MAAM,CAAC,QAAS,KAAI,MAAM,CAAC,QAAA;AACjC,MAAM;AACN,MAAM,OAAO,KAAK,CAAA;AAClB,KAAI;AACJ,GAAE;AACF;AACA,EAAE,OAAO,IAAI,CAAA;AACb,CAAA;AACA;AACA,SAAS,kBAAkB,CAAC,YAAY,EAAS,aAAa,EAAkB;AAChF,EAAE,IAAI,kBAAA,GAAqB,YAAY,CAAC,WAAW,CAAA;AACnD,EAAE,IAAI,mBAAA,GAAsB,aAAa,CAAC,WAAW,CAAA;AACrD;AACA;AACA,EAAE,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,EAAE;AACnD,IAAI,OAAO,IAAI,CAAA;AACf,GAAE;AACF;AACA;AACA,EAAE,IAAI,CAAC,kBAAA,IAAsB,CAAC,mBAAmB,MAAM,CAAC,kBAAA,IAAsB,mBAAmB,CAAC,EAAE;AACpG,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF;AACA,EAAE,kBAAA,GAAqB,kBAAmB,EAAA;AAC1C,EAAE,mBAAA,GAAsB,mBAAoB,EAAA;AAC5C;AACA;AACA,EAAE,IAAI;AACN,IAAI,OAAO,CAAC,EAAE,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAA,KAAM,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;AAC3E,GAAI,CAAA,OAAO,GAAG,EAAE;AAChB,IAAI,OAAO,KAAK,CAAA;AAChB,GAAE;AACF,CAAA;AACA;AACA,SAAS,sBAAsB,CAAC,KAAK,EAAgC;AACrE,EAAE,OAAO,KAAK,CAAC,aAAa,KAAK,CAAC,SAAS,CAAC,MAAO,IAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;AAC/E,CAAA;AACA;AACA,SAAS,mBAAmB,CAAC,KAAK,EAAmC;AACrE,EAAE,MAAM,SAAA,GAAY,KAAK,CAAC,SAAS,CAAA;AACnC;AACA,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,IAAI;AACR;AACA,MAAM,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAA;AAClD,KAAM,CAAA,OAAO,GAAG,EAAE;AAClB,MAAM,OAAO,SAAS,CAAA;AACtB,KAAI;AACJ,GAAE;AACF,EAAE,OAAO,SAAS,CAAA;AAClB;;;;;;"}