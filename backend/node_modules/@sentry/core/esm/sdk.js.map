{"version": 3, "file": "sdk.js", "sources": ["../../src/sdk.ts"], "sourcesContent": ["import type { Client, ClientOptions } from '@sentry/types';\nimport { consoleSandbox, logger } from '@sentry/utils';\n\nimport { DEBUG_BUILD } from './debug-build';\nimport { getCurrentScope } from './exports';\nimport { getCurrentHub } from './hub';\n\n/** A class object that can instantiate Client objects. */\nexport type ClientClass<F extends Client, O extends ClientOptions> = new (options: O) => F;\n\n/**\n * Internal function to create a new SDK client instance. The client is\n * installed and then bound to the current scope.\n *\n * @param clientClass The client class to instantiate.\n * @param options Options to pass to the client.\n */\nexport function initAndBind<F extends Client, O extends ClientOptions>(\n  clientClass: ClientClass<F, O>,\n  options: O,\n): void {\n  if (options.debug === true) {\n    if (DEBUG_BUILD) {\n      logger.enable();\n    } else {\n      // use `console.warn` rather than `logger.warn` since by non-debug bundles have all `logger.x` statements stripped\n      consoleSandbox(() => {\n        // eslint-disable-next-line no-console\n        console.warn('[<PERSON><PERSON>] Cannot initialize SDK with `debug` option using a non-debug bundle.');\n      });\n    }\n  }\n  const scope = getCurrentScope();\n  scope.update(options.initialScope);\n\n  const client = new clientClass(options);\n  setCurrentClient(client);\n  initializeClient(client);\n}\n\n/**\n * Make the given client the current client.\n */\nexport function setCurrentClient(client: Client): void {\n  // eslint-disable-next-line deprecation/deprecation\n  const hub = getCurrentHub();\n  // eslint-disable-next-line deprecation/deprecation\n  const top = hub.getStackTop();\n  top.client = client;\n  top.scope.setClient(client);\n}\n\n/**\n * Initialize the client for the current scope.\n * Make sure to call this after `setCurrentClient()`.\n */\nfunction initializeClient(client: Client): void {\n  if (client.init) {\n    client.init();\n    // TODO v8: Remove this fallback\n    // eslint-disable-next-line deprecation/deprecation\n  } else if (client.setupIntegrations) {\n    // eslint-disable-next-line deprecation/deprecation\n    client.setupIntegrations();\n  }\n}\n"], "names": [], "mappings": ";;;;;AAOA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,WAAW;AAC3B,EAAE,WAAW;AACb,EAAE,OAAO;AACT,EAAQ;AACR,EAAE,IAAI,OAAO,CAAC,KAAM,KAAI,IAAI,EAAE;AAC9B,IAAI,IAAI,WAAW,EAAE;AACrB,MAAM,MAAM,CAAC,MAAM,EAAE,CAAA;AACrB,WAAW;AACX;AACA,MAAM,cAAc,CAAC,MAAM;AAC3B;AACA,QAAQ,OAAO,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAA;AACpG,OAAO,CAAC,CAAA;AACR,KAAI;AACJ,GAAE;AACF,EAAE,MAAM,KAAA,GAAQ,eAAe,EAAE,CAAA;AACjC,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;AACpC;AACA,EAAE,MAAM,MAAO,GAAE,IAAI,WAAW,CAAC,OAAO,CAAC,CAAA;AACzC,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAA;AAC1B,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAA;AAC1B,CAAA;AACA;AACA;AACA;AACA;AACO,SAAS,gBAAgB,CAAC,MAAM,EAAgB;AACvD;AACA,EAAE,MAAM,GAAA,GAAM,aAAa,EAAE,CAAA;AAC7B;AACA,EAAE,MAAM,GAAI,GAAE,GAAG,CAAC,WAAW,EAAE,CAAA;AAC/B,EAAE,GAAG,CAAC,MAAO,GAAE,MAAM,CAAA;AACrB,EAAE,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;AAC7B,CAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,gBAAgB,CAAC,MAAM,EAAgB;AAChD,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE;AACnB,IAAI,MAAM,CAAC,IAAI,EAAE,CAAA;AACjB;AACA;AACA,SAAS,IAAI,MAAM,CAAC,iBAAiB,EAAE;AACvC;AACA,IAAI,MAAM,CAAC,iBAAiB,EAAE,CAAA;AAC9B,GAAE;AACF;;;;"}