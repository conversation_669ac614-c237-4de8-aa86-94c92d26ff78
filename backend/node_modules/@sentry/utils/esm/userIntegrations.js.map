{"version": 3, "file": "userIntegrations.js", "sources": ["../../src/userIntegrations.ts"], "sourcesContent": ["import type { Integration } from '@sentry/types';\n\nexport type UserIntegrationsFunction = (integrations: Integration[]) => Integration[];\nexport type UserIntegrations = Integration[] | UserIntegrationsFunction;\nexport type IntegrationWithExclusionOption = Integration & {\n  /**\n   * Allow the user to exclude this integration by not returning it from a function provided as the `integrations` option\n   * in `Sentry.init()`. Meant to be used with default integrations, the idea being that if a user has actively filtered\n   * an integration out, we should be able to respect that choice if we wish.\n   */\n  allowExclusionByUser?: boolean;\n};\n\ntype ForcedIntegrationOptions = {\n  [keyPath: string]: unknown;\n};\n\n/**\n * Recursively traverses an object to update an existing nested key.\n * Note: The provided key path must include existing properties,\n * the function will not create objects while traversing.\n *\n * @param obj An object to update\n * @param value The value to update the nested key with\n * @param keyPath The path to the key to update ex. fizz.buzz.foo\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction setNestedKey(obj: Record<string, any>, keyPath: string, value: unknown): void {\n  // Ex. foo.bar.zoop will extract foo and bar.zoop\n  const match = keyPath.match(/([a-z_]+)\\.(.*)/i);\n  // The match will be null when there's no more recursing to do, i.e., when we've reached the right level of the object\n  if (match === null) {\n    obj[keyPath] = value;\n  } else {\n    // `match[1]` is the initial segment of the path, and `match[2]` is the remainder of the path\n    const innerObj = obj[match[1]];\n    setNestedKey(innerObj, match[2], value);\n  }\n}\n\n/**\n * Enforces inclusion of a given integration with specified options in an integration array originally determined by the\n * user, by either including the given default instance or by patching an existing user instance with the given options.\n *\n * Ideally this would happen when integrations are set up, but there isn't currently a mechanism there for merging\n * options from a default integration instance with those from a user-provided instance of the same integration, only\n * for allowing the user to override a default instance entirely. (TODO: Fix that.)\n *\n * @param defaultIntegrationInstance An instance of the integration with the correct options already set\n * @param userIntegrations Integrations defined by the user.\n * @param forcedOptions Options with which to patch an existing user-derived instance on the integration.\n * @returns A final integrations array.\n *\n * @deprecated This will be removed in v8.\n */\nexport function addOrUpdateIntegration<T extends UserIntegrations>(\n  defaultIntegrationInstance: Integration,\n  userIntegrations: T,\n  forcedOptions: ForcedIntegrationOptions = {},\n): T {\n  return (\n    Array.isArray(userIntegrations)\n      ? addOrUpdateIntegrationInArray(defaultIntegrationInstance, userIntegrations, forcedOptions)\n      : addOrUpdateIntegrationInFunction(\n          defaultIntegrationInstance,\n          // Somehow TS can't figure out that not being an array makes this necessarily a function\n          userIntegrations as UserIntegrationsFunction,\n          forcedOptions,\n        )\n  ) as T;\n}\n\nfunction addOrUpdateIntegrationInArray(\n  defaultIntegrationInstance: Integration,\n  userIntegrations: Integration[],\n  forcedOptions: ForcedIntegrationOptions,\n): Integration[] {\n  const userInstance = userIntegrations.find(integration => integration.name === defaultIntegrationInstance.name);\n\n  if (userInstance) {\n    for (const [keyPath, value] of Object.entries(forcedOptions)) {\n      setNestedKey(userInstance, keyPath, value);\n    }\n\n    return userIntegrations;\n  }\n\n  return [...userIntegrations, defaultIntegrationInstance];\n}\n\nfunction addOrUpdateIntegrationInFunction(\n  defaultIntegrationInstance: IntegrationWithExclusionOption,\n  userIntegrationsFunc: UserIntegrationsFunction,\n  forcedOptions: ForcedIntegrationOptions,\n): UserIntegrationsFunction {\n  const wrapper: UserIntegrationsFunction = defaultIntegrations => {\n    const userFinalIntegrations = userIntegrationsFunc(defaultIntegrations);\n\n    // There are instances where we want the user to be able to prevent an integration from appearing at all, which they\n    // would do by providing a function which filters out the integration in question. If that's happened in one of\n    // those cases, don't add our default back in.\n    if (defaultIntegrationInstance.allowExclusionByUser) {\n      const userFinalInstance = userFinalIntegrations.find(\n        integration => integration.name === defaultIntegrationInstance.name,\n      );\n      if (!userFinalInstance) {\n        return userFinalIntegrations;\n      }\n    }\n\n    return addOrUpdateIntegrationInArray(defaultIntegrationInstance, userFinalIntegrations, forcedOptions);\n  };\n\n  return wrapper;\n}\n"], "names": [], "mappings": "AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,YAAY,CAAC,GAAG,EAAuB,OAAO,EAAU,KAAK,EAAiB;AACvF;AACA,EAAE,MAAM,QAAQ,OAAO,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAA;AACjD;AACA,EAAE,IAAI,KAAM,KAAI,IAAI,EAAE;AACtB,IAAI,GAAG,CAAC,OAAO,CAAA,GAAI,KAAK,CAAA;AACxB,SAAS;AACT;AACA,IAAI,MAAM,WAAW,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;AAClC,IAAI,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;AAC3C,GAAE;AACF,CAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,sBAAsB;AACtC,EAAE,0BAA0B;AAC5B,EAAE,gBAAgB;AAClB,EAAE,aAAa,GAA6B,EAAE;AAC9C,EAAK;AACL,EAAE;AACF,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAA;AAClC,QAAQ,6BAA6B,CAAC,0BAA0B,EAAE,gBAAgB,EAAE,aAAa,CAAA;AACjG,QAAQ,gCAAgC;AACxC,UAAU,0BAA0B;AACpC;AACA,UAAU,gBAAiB;AAC3B,UAAU,aAAa;AACvB,SAAQ;AACR,KAAI;AACJ,CAAA;AACA;AACA,SAAS,6BAA6B;AACtC,EAAE,0BAA0B;AAC5B,EAAE,gBAAgB;AAClB,EAAE,aAAa;AACf,EAAiB;AACjB,EAAE,MAAM,YAAa,GAAE,gBAAgB,CAAC,IAAI,CAAC,WAAA,IAAe,WAAW,CAAC,IAAK,KAAI,0BAA0B,CAAC,IAAI,CAAC,CAAA;AACjH;AACA,EAAE,IAAI,YAAY,EAAE;AACpB,IAAI,KAAK,MAAM,CAAC,OAAO,EAAE,KAAK,CAAE,IAAG,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;AAClE,MAAM,YAAY,CAAC,YAAY,EAAE,OAAO,EAAE,KAAK,CAAC,CAAA;AAChD,KAAI;AACJ;AACA,IAAI,OAAO,gBAAgB,CAAA;AAC3B,GAAE;AACF;AACA,EAAE,OAAO,CAAC,GAAG,gBAAgB,EAAE,0BAA0B,CAAC,CAAA;AAC1D,CAAA;AACA;AACA,SAAS,gCAAgC;AACzC,EAAE,0BAA0B;AAC5B,EAAE,oBAAoB;AACtB,EAAE,aAAa;AACf,EAA4B;AAC5B,EAAE,MAAM,OAAO,GAA6B,uBAAuB;AACnE,IAAI,MAAM,qBAAsB,GAAE,oBAAoB,CAAC,mBAAmB,CAAC,CAAA;AAC3E;AACA;AACA;AACA;AACA,IAAI,IAAI,0BAA0B,CAAC,oBAAoB,EAAE;AACzD,MAAM,MAAM,iBAAA,GAAoB,qBAAqB,CAAC,IAAI;AAC1D,QAAQ,WAAA,IAAe,WAAW,CAAC,SAAS,0BAA0B,CAAC,IAAI;AAC3E,OAAO,CAAA;AACP,MAAM,IAAI,CAAC,iBAAiB,EAAE;AAC9B,QAAQ,OAAO,qBAAqB,CAAA;AACpC,OAAM;AACN,KAAI;AACJ;AACA,IAAI,OAAO,6BAA6B,CAAC,0BAA0B,EAAE,qBAAqB,EAAE,aAAa,CAAC,CAAA;AAC1G,GAAG,CAAA;AACH;AACA,EAAE,OAAO,OAAO,CAAA;AAChB;;;;"}