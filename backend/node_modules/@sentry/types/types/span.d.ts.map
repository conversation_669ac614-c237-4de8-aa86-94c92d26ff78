{"version": 3, "file": "span.d.ts", "sourceRoot": "", "sources": ["../../src/span.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,WAAW,CAAC;AAC9C,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AACnD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,eAAe,CAAC;AAClD,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AACxC,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AAC9C,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAEjD,KAAK,cAAc,GAAG,QAAQ,GAAG,MAAM,CAAC;AACxC,KAAK,kBAAkB,GAAG,MAAM,CAAC;AACjC,KAAK,yBAAyB,GAAG,MAAM,CAAC;AACxC,KAAK,yBAAyB,GAAG,MAAM,CAAC;AACxC,MAAM,MAAM,UAAU,GAClB,cAAc,GACd,GAAG,cAAc,IAAI,kBAAkB,EAAE,GACzC,GAAG,cAAc,IAAI,kBAAkB,IAAI,yBAAyB,EAAE,GACtE,GAAG,cAAc,IAAI,kBAAkB,IAAI,yBAAyB,IAAI,yBAAyB,EAAE,CAAC;AAGxG,MAAM,MAAM,kBAAkB,GAC1B,MAAM,GACN,MAAM,GACN,OAAO,GACP,KAAK,CAAC,IAAI,GAAG,SAAS,GAAG,MAAM,CAAC,GAChC,KAAK,CAAC,IAAI,GAAG,SAAS,GAAG,MAAM,CAAC,GAChC,KAAK,CAAC,IAAI,GAAG,SAAS,GAAG,OAAO,CAAC,CAAC;AAEtC,MAAM,MAAM,cAAc,GAAG,OAAO,CAAC;IACnC,eAAe,EAAE,MAAM,CAAC;IACxB,WAAW,EAAE,MAAM,CAAC;IACpB,eAAe,EAAE,MAAM,CAAC;IACxB,oBAAoB,EAAE,MAAM,CAAC;CAC9B,CAAC,GACA,MAAM,CAAC,MAAM,EAAE,kBAAkB,GAAG,SAAS,CAAC,CAAC;AAEjD,MAAM,MAAM,aAAa,GAAG;IAC1B,GAAG,EAAE,MAAM,CAAC;IACZ,GAAG,EAAE,MAAM,CAAC;IACZ,KAAK,EAAE,MAAM,CAAC;IACd,GAAG,EAAE,MAAM,CAAC;IACZ,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,SAAS,CAAC;CAC9C,CAAC;AAEF,kEAAkE;AAClE,MAAM,MAAM,aAAa,GAAG,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC;AAEnD,uCAAuC;AACvC,MAAM,WAAW,QAAQ;IACvB,IAAI,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,CAAC;IAC9B,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,OAAO,EAAE,MAAM,CAAC;IAChB,eAAe,EAAE,MAAM,CAAC;IACxB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,IAAI,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,CAAC;IACpC,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,QAAQ,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,EAAE,UAAU,CAAC;IACpB,gBAAgB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;IACxD,cAAc,CAAC,EAAE,MAAM,CAAC;CACzB;AAGD,KAAK,aAAa,GAAG,GAAG,CAAC;AACzB,KAAK,gBAAgB,GAAG,GAAG,CAAC;AAC5B,MAAM,MAAM,SAAS,GAAG,aAAa,GAAG,gBAAgB,CAAC;AAEzD,MAAM,WAAW,eAAe;IAC9B;;;;;OAKG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;;;OAIG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC;IAEnB;;;;;;;OAOG;IACH,UAAU,EAAE,SAAS,CAAC;CAGvB;AAED,8EAA8E;AAC9E,MAAM,WAAW,WAAW;IAC1B;;;;OAIG;IACH,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAEjC;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE1B;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAExB;;;OAGG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE5B;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAElC;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAE9B;;OAEG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE5B;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE7B;;;OAGG;IACH,IAAI,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,CAAC;IAEpC;;;OAGG;IACH,IAAI,CAAC,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,CAAC;IAE9B;;OAEG;IACH,UAAU,CAAC,EAAE,cAAc,CAAC;IAE5B;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAEpC;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAElC;;OAEG;IACH,YAAY,CAAC,EAAE,YAAY,GAAG,SAAS,CAAC;IAExC;;OAEG;IACH,MAAM,CAAC,EAAE,UAAU,GAAG,SAAS,CAAC;IAEhC;;OAEG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IAEvB;;OAEG;IACH,YAAY,CAAC,EAAE,YAAY,CAAC;CAC7B;AAED,qCAAqC;AACrC,MAAM,WAAW,IAAK,SAAQ,IAAI,CAAC,WAAW,EAAE,IAAI,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACzE;;;OAGG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;;;;OAKG;IACH,EAAE,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAExB;;;OAGG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;;;OAIG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAElC;;;OAGG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;;OAGG;IACH,OAAO,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAE9B;;;OAGG;IACH,cAAc,EAAE,MAAM,CAAC;IAEvB;;;OAGG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAElC;;;OAGG;IACH,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE,CAAC;IAEnC;;;OAGG;IACH,IAAI,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,GAAG,CAAA;KAAE,CAAC;IAE7B;;;OAGG;IACH,UAAU,EAAE,cAAc,CAAC;IAE3B;;;OAGG;IACH,WAAW,CAAC,EAAE,WAAW,CAAC;IAE1B;;;;OAIG;IACH,YAAY,EAAE,YAAY,CAAC;IAE3B;;;;;;OAMG;IACH,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAE5B;;;;OAIG;IACH,MAAM,CAAC,EAAE,UAAU,GAAG,SAAS,CAAC;IAEhC;;;OAGG;IACH,WAAW,IAAI,eAAe,CAAC;IAE/B;;;;;;OAMG;IACH,MAAM,CAAC,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;IAEpC;;OAEG;IACH,GAAG,CAAC,YAAY,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC;IAExC;;;;;;;;OAQG;IACH,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,GAAG,IAAI,CAAC;IAE5C;;;;;OAKG;IACH,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,IAAI,CAAC;IAEvC;;;OAGG;IACH,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,kBAAkB,GAAG,SAAS,GAAG,IAAI,CAAC;IAEvE;;;OAGG;IACH,aAAa,CAAC,UAAU,EAAE,cAAc,GAAG,IAAI,CAAC;IAEhD;;;;OAIG;IACH,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IAEhC;;;;OAIG;IACH,aAAa,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC;IAExC;;;;OAIG;IACH,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IAE5B;;OAEG;IACH,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IAE/B;;;;;OAKG;IACH,UAAU,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,MAAM,WAAW,EAAE,SAAS,GAAG,SAAS,GAAG,cAAc,CAAC,CAAC,GAAG,IAAI,CAAC;IAEtH;;;;OAIG;IACH,SAAS,IAAI,OAAO,CAAC;IAErB;;;OAGG;IACH,aAAa,IAAI,MAAM,CAAC;IAExB;;;OAGG;IACH,SAAS,IAAI,WAAW,CAAC;IAEzB;;;OAGG;IACH,iBAAiB,CAAC,WAAW,EAAE,WAAW,GAAG,IAAI,CAAC;IAElD;;;OAGG;IACH,eAAe,IAAI,YAAY,CAAC;IAEhC;;;OAGG;IACH,MAAM,IAAI,QAAQ,CAAC;IAEnB;;;OAGG;IACH,WAAW,IAAI,OAAO,CAAC;CACxB"}