{"name": "forwarded-parse", "version": "2.1.2", "description": "Parse the Forwarded header (RFC 7239) into an array of objects", "keywords": ["forwarded", "RFC-7239", "rfc-7239", "RFC7239", "rfc7239", "header", "parser", "7239", "RFC", "rfc"], "homepage": "https://github.com/lpinca/forwarded-parse", "bugs": "https://github.com/lpinca/forwarded-parse/issues", "license": "MIT", "author": "<PERSON>", "files": ["lib", "index.js", "index.d.ts"], "main": "index.js", "repository": "lpinca/forwarded-parse", "scripts": {"test": "c8 --reporter=lcov --reporter=text tape test.js"}, "devDependencies": {"c8": "^7.3.0", "pre-commit": "^1.2.2", "tape": "^5.0.1"}}