{"name": "component-emitter", "description": "Event emitter", "version": "1.3.1", "license": "MIT", "funding": "https://github.com/sponsors/sindresorhus", "devDependencies": {"mocha": "*", "should": "*"}, "component": {"scripts": {"emitter/index.js": "index.js"}}, "main": "index.js", "repository": {"type": "git", "url": "https://github.com/sindresorhus/component-emitter.git"}, "scripts": {"test": "make test"}, "files": ["index.js", "LICENSE"]}