{"version": 3, "file": "Resource.js", "sourceRoot": "", "sources": ["../../src/Resource.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,4CAA0C;AAC1C,8EAK6C;AAC7C,8CAA+C;AAE/C,yCAAgD;AAGhD;;;GAGG;AACH,MAAa,QAAQ;IAoCnB;IACE;;;;OAIG;IACH,UAA8B,EAC9B,sBAAoD;;QAEpD,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAC9B,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,IAAI,IAAI,CAAC;QAC7D,IAAI,CAAC,eAAe,GAAG,MAAA,IAAI,CAAC,WAAW,mCAAI,EAAE,CAAC;QAC9C,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,aAAtB,sBAAsB,uBAAtB,sBAAsB,CAAE,IAAI,CACzD,eAAe,CAAC,EAAE;YAChB,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YACxE,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;YACpC,OAAO,eAAe,CAAC;QACzB,CAAC,EACD,GAAG,CAAC,EAAE;YACJ,UAAI,CAAC,KAAK,CAAC,oDAAoD,EAAE,GAAG,CAAC,CAAC;YACtE,IAAI,CAAC,sBAAsB,GAAG,KAAK,CAAC;YACpC,OAAO,EAAE,CAAC;QACZ,CAAC,CACF,CAAC;IACJ,CAAC;IA9CD;;OAEG;IACH,MAAM,CAAC,KAAK;QACV,OAAO,QAAQ,CAAC,KAAK,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO;QACZ,OAAO,IAAI,QAAQ,CAAC;YAClB,CAAC,+CAAwB,CAAC,EAAE,IAAA,6BAAkB,GAAE;YAChD,CAAC,yDAAkC,CAAC,EAClC,eAAQ,CAAC,yDAAkC,CAAC;YAC9C,CAAC,qDAA8B,CAAC,EAC9B,eAAQ,CAAC,qDAA8B,CAAC;YAC1C,CAAC,wDAAiC,CAAC,EACjC,eAAQ,CAAC,wDAAiC,CAAC;SAC9C,CAAC,CAAC;IACL,CAAC;IA4BD,IAAI,UAAU;;QACZ,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,UAAI,CAAC,KAAK,CACR,+DAA+D,CAChE,CAAC;SACH;QAED,OAAO,MAAA,IAAI,CAAC,WAAW,mCAAI,EAAE,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,sBAAsB;QAC1B,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC/B,MAAM,IAAI,CAAC,uBAAuB,CAAC;SACpC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,KAAK,CAAC,KAAuB;;QAC3B,IAAI,CAAC,KAAK;YAAE,OAAO,IAAI,CAAC;QAExB,8EAA8E;QAC9E,MAAM,oBAAoB,mCACrB,IAAI,CAAC,eAAe,GAEpB,CAAC,MAAC,KAAkB,CAAC,eAAe,mCAAI,KAAK,CAAC,UAAU,CAAC,CAC7D,CAAC;QAEF,IACE,CAAC,IAAI,CAAC,uBAAuB;YAC7B,CAAE,KAAkB,CAAC,uBAAuB,EAC5C;YACA,OAAO,IAAI,QAAQ,CAAC,oBAAoB,CAAC,CAAC;SAC3C;QAED,MAAM,uBAAuB,GAAG,OAAO,CAAC,GAAG,CAAC;YAC1C,IAAI,CAAC,uBAAuB;YAC3B,KAAkB,CAAC,uBAAuB;SAC5C,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB,EAAE,oBAAoB,CAAC,EAAE,EAAE;;YACtD,mEACK,IAAI,CAAC,eAAe,GACpB,mBAAmB,GAEnB,CAAC,MAAC,KAAkB,CAAC,eAAe,mCAAI,KAAK,CAAC,UAAU,CAAC,GACzD,oBAAoB,EACvB;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,QAAQ,CAAC,oBAAoB,EAAE,uBAAuB,CAAC,CAAC;IACrE,CAAC;;AA1HH,4BA2HC;AA1HiB,cAAK,GAAG,IAAI,QAAQ,CAAC,EAAE,CAAC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { diag } from '@opentelemetry/api';\nimport {\n  SEMRESATTRS_SERVICE_NAME,\n  SEMRESATTRS_TELEMETRY_SDK_LANGUAGE,\n  SEMRESATTRS_TELEMETRY_SDK_NAME,\n  SEMRESATTRS_TELEMETRY_SDK_VERSION,\n} from '@opentelemetry/semantic-conventions';\nimport { SDK_INFO } from '@opentelemetry/core';\nimport { ResourceAttributes } from './types';\nimport { defaultServiceName } from './platform';\nimport { IResource } from './IResource';\n\n/**\n * A Resource describes the entity for which a signals (metrics or trace) are\n * collected.\n */\nexport class Resource implements IResource {\n  static readonly EMPTY = new Resource({});\n  private _syncAttributes?: ResourceAttributes;\n  private _asyncAttributesPromise?: Promise<ResourceAttributes>;\n  private _attributes?: ResourceAttributes;\n\n  /**\n   * Check if async attributes have resolved. This is useful to avoid awaiting\n   * waitForAsyncAttributes (which will introduce asynchronous behavior) when not necessary.\n   *\n   * @returns true if the resource \"attributes\" property is not yet settled to its final value\n   */\n  public asyncAttributesPending?: boolean;\n\n  /**\n   * Returns an empty Resource\n   */\n  static empty(): IResource {\n    return Resource.EMPTY;\n  }\n\n  /**\n   * Returns a Resource that identifies the SDK in use.\n   */\n  static default(): IResource {\n    return new Resource({\n      [SEMRESATTRS_SERVICE_NAME]: defaultServiceName(),\n      [SEMRESATTRS_TELEMETRY_SDK_LANGUAGE]:\n        SDK_INFO[SEMRESATTRS_TELEMETRY_SDK_LANGUAGE],\n      [SEMRESATTRS_TELEMETRY_SDK_NAME]:\n        SDK_INFO[SEMRESATTRS_TELEMETRY_SDK_NAME],\n      [SEMRESATTRS_TELEMETRY_SDK_VERSION]:\n        SDK_INFO[SEMRESATTRS_TELEMETRY_SDK_VERSION],\n    });\n  }\n\n  constructor(\n    /**\n     * A dictionary of attributes with string keys and values that provide\n     * information about the entity as numbers, strings or booleans\n     * TODO: Consider to add check/validation on attributes.\n     */\n    attributes: ResourceAttributes,\n    asyncAttributesPromise?: Promise<ResourceAttributes>\n  ) {\n    this._attributes = attributes;\n    this.asyncAttributesPending = asyncAttributesPromise != null;\n    this._syncAttributes = this._attributes ?? {};\n    this._asyncAttributesPromise = asyncAttributesPromise?.then(\n      asyncAttributes => {\n        this._attributes = Object.assign({}, this._attributes, asyncAttributes);\n        this.asyncAttributesPending = false;\n        return asyncAttributes;\n      },\n      err => {\n        diag.debug(\"a resource's async attributes promise rejected: %s\", err);\n        this.asyncAttributesPending = false;\n        return {};\n      }\n    );\n  }\n\n  get attributes(): ResourceAttributes {\n    if (this.asyncAttributesPending) {\n      diag.error(\n        'Accessing resource attributes before async attributes settled'\n      );\n    }\n\n    return this._attributes ?? {};\n  }\n\n  /**\n   * Returns a promise that will never be rejected. Resolves when all async attributes have finished being added to\n   * this Resource's attributes. This is useful in exporters to block until resource detection\n   * has finished.\n   */\n  async waitForAsyncAttributes?(): Promise<void> {\n    if (this.asyncAttributesPending) {\n      await this._asyncAttributesPromise;\n    }\n  }\n\n  /**\n   * Returns a new, merged {@link Resource} by merging the current Resource\n   * with the other Resource. In case of a collision, other Resource takes\n   * precedence.\n   *\n   * @param other the Resource that will be merged with this.\n   * @returns the newly merged Resource.\n   */\n  merge(other: IResource | null): IResource {\n    if (!other) return this;\n\n    // SpanAttributes from other resource overwrite attributes from this resource.\n    const mergedSyncAttributes = {\n      ...this._syncAttributes,\n      //Support for old resource implementation where _syncAttributes is not defined\n      ...((other as Resource)._syncAttributes ?? other.attributes),\n    };\n\n    if (\n      !this._asyncAttributesPromise &&\n      !(other as Resource)._asyncAttributesPromise\n    ) {\n      return new Resource(mergedSyncAttributes);\n    }\n\n    const mergedAttributesPromise = Promise.all([\n      this._asyncAttributesPromise,\n      (other as Resource)._asyncAttributesPromise,\n    ]).then(([thisAsyncAttributes, otherAsyncAttributes]) => {\n      return {\n        ...this._syncAttributes,\n        ...thisAsyncAttributes,\n        //Support for old resource implementation where _syncAttributes is not defined\n        ...((other as Resource)._syncAttributes ?? other.attributes),\n        ...otherAsyncAttributes,\n      };\n    });\n\n    return new Resource(mergedSyncAttributes, mergedAttributesPromise);\n  }\n}\n"]}