{"version": 3, "file": "DeltaMetricProcessor.js", "sourceRoot": "", "sources": ["../../../src/state/DeltaMetricProcessor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;AAGH,OAAO,EAAS,cAAc,EAAE,MAAM,UAAU,CAAC;AAEjD,OAAO,EAAE,gBAAgB,EAAE,MAAM,WAAW,CAAC;AAE7C;;;;;;GAMG;AACH;IASE,8BACU,WAA0B,EAClC,2BAAoC;QAD5B,gBAAW,GAAX,WAAW,CAAe;QAT5B,6BAAwB,GAAG,IAAI,gBAAgB,EAAK,CAAC;QAC7D,kDAAkD;QAClD,0EAA0E;QAClE,2BAAsB,GAAG,IAAI,gBAAgB,EAAK,CAAC;QAEnD,wBAAmB,GAAG,EAAE,sBAAsB,EAAE,IAAI,EAAE,CAAC;QAO7D,IAAI,CAAC,iBAAiB,GAAG,CAAC,2BAA2B,aAA3B,2BAA2B,cAA3B,2BAA2B,GAAI,IAAI,CAAC,GAAG,CAAC,CAAC;QACnE,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACpE,CAAC;IAED,qCAAM,GAAN,UACE,KAAa,EACb,UAAsB,EACtB,QAAiB,EACjB,cAAsB;QAJxB,iBAuBC;QAjBC,IAAI,YAAY,GAAG,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAEjE,IAAI,CAAC,YAAY,EAAE;YACjB,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBAChE,IAAM,oBAAoB,GAAG,IAAI,CAAC,wBAAwB,CAAC,YAAY,CACrE,IAAI,CAAC,mBAAmB,EACxB,cAAM,OAAA,KAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,cAAc,CAAC,EAAnD,CAAmD,CAC1D,CAAC;gBACF,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,MAAM,CAAC,KAAK,CAAC,CAAC;gBACpC,OAAO;aACR;YAED,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;YACnE,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;SAC7D;QAED,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,MAAM,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAED,4CAAa,GAAb,UACE,YAAsC,EACtC,cAAsB;QAFxB,iBAiDC;QA7CC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CACxC,UAAC,EAA6B;gBAA7B,KAAA,aAA6B,EAA5B,UAAU,QAAA,EAAE,KAAK,QAAA,EAAE,QAAQ,QAAA;YAC3B,IAAM,YAAY,GAChB,KAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;YACtD,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5B,IAAI,KAAK,GAAG,YAAY,CAAC;YACzB,sCAAsC;YACtC,IAAI,KAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE;gBACzD,4CAA4C;gBAC5C,oEAAoE;gBACpE,IAAM,QAAQ,GAAG,KAAI,CAAC,sBAAsB,CAAC,GAAG,CAC9C,UAAU,EACV,QAAQ,CACR,CAAC;gBACH,KAAK,GAAG,KAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;aACvD;iBAAM;gBACL,wEAAwE;gBACxE,IAAI,KAAI,CAAC,sBAAsB,CAAC,IAAI,IAAI,KAAI,CAAC,iBAAiB,EAAE;oBAC9D,UAAU,GAAG,KAAI,CAAC,mBAAmB,CAAC;oBACtC,QAAQ,GAAG,KAAI,CAAC,iBAAiB,CAAC;oBAClC,IAAI,KAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE;wBACzD,IAAM,QAAQ,GAAG,KAAI,CAAC,sBAAsB,CAAC,GAAG,CAC9C,UAAU,EACV,QAAQ,CACR,CAAC;wBACH,KAAK,GAAG,KAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;qBACvD;iBACF;aACF;YACD,uCAAuC;YACvC,IAAI,KAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE;gBAC3D,4CAA4C;gBAC5C,oEAAoE;gBACpE,IAAM,MAAM,GAAG,KAAI,CAAC,wBAAwB,CAAC,GAAG,CAC9C,UAAU,EACV,QAAQ,CACR,CAAC;gBACH,KAAK,GAAG,KAAI,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;aAC/C;YAED,gDAAgD;YAChD,KAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,UAAU,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC;YACpE,KAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACjE,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;OAGG;IACH,sCAAO,GAAP;QACE,IAAM,eAAe,GAAG,IAAI,CAAC,wBAAwB,CAAC;QACtD,IAAI,CAAC,wBAAwB,GAAG,IAAI,gBAAgB,EAAE,CAAC;QAEvD,OAAO,eAAe,CAAC;IACzB,CAAC;IACH,2BAAC;AAAD,CAAC,AAvGD,IAuGC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, HrTime, Attributes } from '@opentelemetry/api';\nimport { Maybe, hashAttributes } from '../utils';\nimport { Accumulation, Aggregator } from '../aggregator/types';\nimport { AttributeHashMap } from './HashMap';\n\n/**\n * Internal interface.\n *\n * Allows synchronous collection of metrics. This processor should allow\n * allocation of new aggregation cells for metrics and convert cumulative\n * recording to delta data points.\n */\nexport class DeltaMetricProcessor<T extends Maybe<Accumulation>> {\n  private _activeCollectionStorage = new AttributeHashMap<T>();\n  // TODO: find a reasonable mean to clean the memo;\n  // https://github.com/open-telemetry/opentelemetry-specification/pull/2208\n  private _cumulativeMemoStorage = new AttributeHashMap<T>();\n  private _cardinalityLimit: number;\n  private _overflowAttributes = { 'otel.metric.overflow': true };\n  private _overflowHashCode: string;\n\n  constructor(\n    private _aggregator: Aggregator<T>,\n    aggregationCardinalityLimit?: number\n  ) {\n    this._cardinalityLimit = (aggregationCardinalityLimit ?? 2000) - 1;\n    this._overflowHashCode = hashAttributes(this._overflowAttributes);\n  }\n\n  record(\n    value: number,\n    attributes: Attributes,\n    _context: Context,\n    collectionTime: HrTime\n  ) {\n    let accumulation = this._activeCollectionStorage.get(attributes);\n\n    if (!accumulation) {\n      if (this._activeCollectionStorage.size >= this._cardinalityLimit) {\n        const overflowAccumulation = this._activeCollectionStorage.getOrDefault(\n          this._overflowAttributes,\n          () => this._aggregator.createAccumulation(collectionTime)\n        );\n        overflowAccumulation?.record(value);\n        return;\n      }\n\n      accumulation = this._aggregator.createAccumulation(collectionTime);\n      this._activeCollectionStorage.set(attributes, accumulation);\n    }\n\n    accumulation?.record(value);\n  }\n\n  batchCumulate(\n    measurements: AttributeHashMap<number>,\n    collectionTime: HrTime\n  ) {\n    Array.from(measurements.entries()).forEach(\n      ([attributes, value, hashCode]) => {\n        const accumulation =\n          this._aggregator.createAccumulation(collectionTime);\n        accumulation?.record(value);\n        let delta = accumulation;\n        // Diff with recorded cumulative memo.\n        if (this._cumulativeMemoStorage.has(attributes, hashCode)) {\n          // has() returned true, previous is present.\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          const previous = this._cumulativeMemoStorage.get(\n            attributes,\n            hashCode\n          )!;\n          delta = this._aggregator.diff(previous, accumulation);\n        } else {\n          // If the cardinality limit is reached, we need to change the attributes\n          if (this._cumulativeMemoStorage.size >= this._cardinalityLimit) {\n            attributes = this._overflowAttributes;\n            hashCode = this._overflowHashCode;\n            if (this._cumulativeMemoStorage.has(attributes, hashCode)) {\n              const previous = this._cumulativeMemoStorage.get(\n                attributes,\n                hashCode\n              )!;\n              delta = this._aggregator.diff(previous, accumulation);\n            }\n          }\n        }\n        // Merge with uncollected active delta.\n        if (this._activeCollectionStorage.has(attributes, hashCode)) {\n          // has() returned true, previous is present.\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          const active = this._activeCollectionStorage.get(\n            attributes,\n            hashCode\n          )!;\n          delta = this._aggregator.merge(active, delta);\n        }\n\n        // Save the current record and the delta record.\n        this._cumulativeMemoStorage.set(attributes, accumulation, hashCode);\n        this._activeCollectionStorage.set(attributes, delta, hashCode);\n      }\n    );\n  }\n\n  /**\n   * Returns a collection of delta metrics. Start time is the when first\n   * time event collected.\n   */\n  collect() {\n    const unreportedDelta = this._activeCollectionStorage;\n    this._activeCollectionStorage = new AttributeHashMap();\n\n    return unreportedDelta;\n  }\n}\n"]}