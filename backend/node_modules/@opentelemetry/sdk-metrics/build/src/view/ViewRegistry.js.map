{"version": 3, "file": "ViewRegistry.js", "sourceRoot": "", "sources": ["../../../src/view/ViewRegistry.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAQH,MAAa,YAAY;IAAzB;QACU,qBAAgB,GAAW,EAAE,CAAC;IA4CxC,CAAC;IA1CC,OAAO,CAAC,IAAU;QAChB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACnC,CAAC;IAED,SAAS,CACP,UAAgC,EAChC,KAA2B;QAE3B,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,cAAc,CAAC,EAAE;YAC1D,OAAO,CACL,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,kBAAkB,EAAE,UAAU,CAAC;gBACpE,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,aAAa,EAAE,KAAK,CAAC,CACtD,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAEO,gBAAgB,CACtB,QAA4B,EAC5B,UAAgC;QAEhC,OAAO,CACL,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,SAAS;YAC/B,UAAU,CAAC,IAAI,KAAK,QAAQ,CAAC,OAAO,EAAE,CAAC;YACzC,QAAQ,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC;YAC/C,QAAQ,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAChD,CAAC;IACJ,CAAC;IAEO,WAAW,CACjB,QAAuB,EACvB,KAA2B;QAE3B,OAAO,CACL,QAAQ,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;YAC1C,CAAC,KAAK,CAAC,OAAO,KAAK,SAAS;gBAC1B,QAAQ,CAAC,gBAAgB,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACnD,CAAC,KAAK,CAAC,SAAS,KAAK,SAAS;gBAC5B,QAAQ,CAAC,kBAAkB,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CACxD,CAAC;IACJ,CAAC;CACF;AA7CD,oCA6CC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { InstrumentationScope } from '@opentelemetry/core';\nimport { InstrumentDescriptor } from '../InstrumentDescriptor';\nimport { InstrumentSelector } from './InstrumentSelector';\nimport { MeterSelector } from './MeterSelector';\nimport { View } from './View';\n\nexport class ViewRegistry {\n  private _registeredViews: View[] = [];\n\n  addView(view: View) {\n    this._registeredViews.push(view);\n  }\n\n  findViews(\n    instrument: InstrumentDescriptor,\n    meter: InstrumentationScope\n  ): View[] {\n    const views = this._registeredViews.filter(registeredView => {\n      return (\n        this._matchInstrument(registeredView.instrumentSelector, instrument) &&\n        this._matchMeter(registeredView.meterSelector, meter)\n      );\n    });\n\n    return views;\n  }\n\n  private _matchInstrument(\n    selector: InstrumentSelector,\n    instrument: InstrumentDescriptor\n  ): boolean {\n    return (\n      (selector.getType() === undefined ||\n        instrument.type === selector.getType()) &&\n      selector.getNameFilter().match(instrument.name) &&\n      selector.getUnitFilter().match(instrument.unit)\n    );\n  }\n\n  private _matchMeter(\n    selector: MeterSelector,\n    meter: InstrumentationScope\n  ): boolean {\n    return (\n      selector.getNameFilter().match(meter.name) &&\n      (meter.version === undefined ||\n        selector.getVersionFilter().match(meter.version)) &&\n      (meter.schemaUrl === undefined ||\n        selector.getSchemaUrlFilter().match(meter.schemaUrl))\n    );\n  }\n}\n"]}