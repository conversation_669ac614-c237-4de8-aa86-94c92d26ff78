{"name": "backend", "version": "1.0.0", "description": "", "main": "src/server.js", "scripts": {"test": "jest", "start": "node src/server.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@google/generative-ai": "^0.24.1", "@sentry/node": "^7.118.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.0", "prom-client": "^15.1.0", "socket.io": "^4.8.1", "winston": "^3.11.0", "winston-elasticsearch": "^0.17.4", "xss-clean": "^0.1.4", "hpp": "^0.2.3"}, "devDependencies": {"jest": "^30.0.5", "supertest": "^7.1.4"}}