require('dotenv').config();
const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const http = require('http');
const { Server } = require('socket.io');

// Import monitoring and logging
const logger = require('./config/logger');
const { connectRedis } = require('./config/redis');
const { initSentry, sentryRequestHandler, sentryTracingHandler, sentryErrorHandler } = require('./config/sentry');
const { register, collectHttpMetrics, updateBusinessMetrics } = require('./middleware/metrics');
const {
  rateLimiters,
  helmetConfig,
  sanitizeInput,
  requestLogger,
  securityHeaders,
  corsOptions
} = require('./middleware/security');

const app = express();

// Initialize Sentry
initSentry(app);

// Initialize Redis
connectRedis();

const httpServer = http.createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: process.env.CLIENT_URL || 'http://localhost:3000',
    methods: ['GET', 'POST'],
  },
});

io.on('connection', (socket) => {
  logger.info('A user connected');
  // Join a room for farm-specific notifications
  socket.on('joinFarmRoom', (farmId) => {
    socket.join(farmId);
    logger.info(`User joined farm room: ${farmId}`);
  });

  socket.on('disconnect', () => {
    logger.info('User disconnected');
  });
});

// Make io accessible to routes
app.set('socketio', io);

// Sentry request handler (must be first)
app.use(sentryRequestHandler());
app.use(sentryTracingHandler());

// Security headers
app.use(securityHeaders);

// Request logging
app.use(requestLogger);

// Metrics collection middleware
app.use(collectHttpMetrics);

// Security Middleware
app.use(helmetConfig);

// Input sanitization
app.use(sanitizeInput);

// Rate Limiting
app.use(rateLimiters.general);

// Middleware
app.use(express.json({ limit: '10mb' })); // Body parser for JSON
app.use(cors(corsOptions)); // Enable CORS with security options

// Database Connection
const mongoURI = process.env.MONGO_URI || 'mongodb://localhost:27017/smartpoultry';
mongoose.connect(mongoURI)
  .then(() => {
    logger.info('MongoDB connected successfully!');
    // Initialize business metrics after DB connection
    updateBusinessMetrics();
  })
  .catch(err => {
    logger.error('MongoDB connection error:', err);
    process.exit(1);
  });

// Basic Route
const authRoutes = require('./routes/authRoutes');
const farmRoutes = require('./routes/farmRoutes');
const chickenRoutes = require('./routes/chickenRoutes');
const eggProductionRoutes = require('./routes/eggProductionRoutes');
const feedRecordRoutes = require('./routes/feedRecordRoutes');
const healthRecordRoutes = require('./routes/healthRecordRoutes');
const financialRecordRoutes = require('./routes/financialRecordRoutes');
const supplierRoutes = require('./routes/supplierRoutes');
const customerRoutes = require('./routes/customerRoutes');
const analyticsRoutes = require('./routes/analyticsRoutes');
const notificationRoutes = require('./routes/notificationRoutes');
const userRoutes = require('./routes/userRoutes');
const aiInsightLogRoutes = require('./routes/aiInsightLogRoutes');

// Routes with specific rate limiting
app.use('/api/v1/auth', rateLimiters.auth, authRoutes);
app.use('/api/v1/farms', farmRoutes);
app.use('/api/v1/farms/:farmId/chickens', chickenRoutes);
app.use('/api/v1/farms/:farmId/egg-production', eggProductionRoutes);
app.use('/api/v1/farms/:farmId/feed-records', feedRecordRoutes);
app.use('/api/v1/farms/:farmId/health-records', healthRecordRoutes);
app.use('/api/v1/farms/:farmId/financial-records', financialRecordRoutes);
app.use('/api/v1/farms/:farmId/suppliers', supplierRoutes);
app.use('/api/v1/farms/:farmId/customers', customerRoutes);
app.use('/api/v1/farms/:farmId/analytics', analyticsRoutes);
app.use('/api/v1/farms/:farmId/notifications', notificationRoutes);
app.use('/api/v1/farms/:farmId/dashboard', dashboardRoutes);
app.use('/api/v1/farms/:farmId/ai-insights', aiInsightLogRoutes);
app.use('/api/v1/users', userRoutes);

app.get('/', (req, res) => {
  res.send('SmartPoultry Backend API is running!');
});

// Health check endpoint
app.get('/health', (req, res) => {
  const healthCheck = {
    uptime: process.uptime(),
    message: 'OK',
    timestamp: Date.now(),
    database: mongoose.connection.readyState === 1 ? 'connected' : 'disconnected',
    environment: process.env.NODE_ENV || 'development'
  };

  try {
    res.status(200).json(healthCheck);
  } catch (error) {
    healthCheck.message = error;
    res.status(503).json(healthCheck);
  }
});

// Metrics endpoint for Prometheus
app.get('/metrics', async (req, res) => {
  try {
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
  } catch (error) {
    logger.error('Error generating metrics:', error);
    res.status(500).end(error.message);
  }
});

// Sentry error handler (must be before other error handlers)
app.use(sentryErrorHandler());

// Error Handling Middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error:', err);

  // Don't leak error details in production
  const message = process.env.NODE_ENV === 'production'
    ? 'Internal server error'
    : err.message;

  res.status(err.status || 500).json({
    message,
    ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
  });
});

const PORT = process.env.PORT || 5000;
httpServer.listen(PORT, () => {
  logger.info(`Server running on port ${PORT}`);
});




