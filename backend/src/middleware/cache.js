const { getRedisClient } = require('../config/redis');
const logger = require('../config/logger');

// Cache middleware factory
const createCacheMiddleware = (options = {}) => {
  const {
    ttl = 300, // 5 minutes default
    keyGenerator = (req) => `${req.method}:${req.originalUrl}`,
    condition = () => true,
    skipCache = (req) => req.method !== 'GET'
  } = options;

  return async (req, res, next) => {
    // Skip caching if condition not met
    if (skipCache(req) || !condition(req)) {
      return next();
    }

    const redis = getRedisClient();
    if (!redis) {
      return next();
    }

    const cacheKey = keyGenerator(req);
    
    try {
      // Try to get from cache
      const cachedData = await redis.get(cacheKey);
      
      if (cachedData) {
        logger.debug(`Cache hit for key: ${cacheKey}`);
        res.setHeader('X-Cache', 'HIT');
        return res.json(JSON.parse(cachedData));
      }

      // Cache miss - intercept response
      const originalSend = res.json;
      res.json = function(data) {
        // Only cache successful responses
        if (res.statusCode >= 200 && res.statusCode < 300) {
          redis.setex(cacheKey, ttl, JSON.stringify(data))
            .catch(err => logger.error('Cache set error:', err));
          res.setHeader('X-Cache', 'MISS');
        }
        
        return originalSend.call(this, data);
      };

      next();
    } catch (error) {
      logger.error('Cache middleware error:', error);
      next();
    }
  };
};

// Specific cache configurations
const cacheConfigs = {
  // Short-term cache for frequently accessed data
  short: createCacheMiddleware({
    ttl: 60, // 1 minute
    keyGenerator: (req) => `short:${req.user?.id}:${req.method}:${req.originalUrl}`
  }),

  // Medium-term cache for dashboard data
  medium: createCacheMiddleware({
    ttl: 300, // 5 minutes
    keyGenerator: (req) => `medium:${req.user?.id}:${req.params.farmId}:${req.originalUrl}`
  }),

  // Long-term cache for analytics and reports
  long: createCacheMiddleware({
    ttl: 1800, // 30 minutes
    keyGenerator: (req) => `long:${req.user?.id}:${req.params.farmId}:${req.originalUrl}`
  }),

  // User-specific cache
  user: createCacheMiddleware({
    ttl: 600, // 10 minutes
    keyGenerator: (req) => `user:${req.user?.id}:${req.originalUrl}`,
    condition: (req) => !!req.user
  }),

  // Farm-specific cache
  farm: createCacheMiddleware({
    ttl: 300, // 5 minutes
    keyGenerator: (req) => `farm:${req.params.farmId}:${req.originalUrl}`,
    condition: (req) => !!req.params.farmId
  })
};

// Cache invalidation helpers
const invalidateCache = async (pattern) => {
  const redis = getRedisClient();
  if (!redis) return;

  try {
    const keys = await redis.keys(pattern);
    if (keys.length > 0) {
      await redis.del(...keys);
      logger.info(`Invalidated ${keys.length} cache keys matching pattern: ${pattern}`);
    }
  } catch (error) {
    logger.error('Cache invalidation error:', error);
  }
};

// Cache invalidation middleware for write operations
const invalidateCacheOnWrite = (patterns = []) => {
  return async (req, res, next) => {
    const originalSend = res.json;
    
    res.json = function(data) {
      // Only invalidate on successful write operations
      if (res.statusCode >= 200 && res.statusCode < 300 && 
          ['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method)) {
        
        // Generate invalidation patterns
        const invalidationPatterns = patterns.map(pattern => {
          if (typeof pattern === 'function') {
            return pattern(req);
          }
          return pattern
            .replace(':userId', req.user?.id || '*')
            .replace(':farmId', req.params?.farmId || '*');
        });

        // Invalidate cache asynchronously
        Promise.all(invalidationPatterns.map(pattern => invalidateCache(pattern)))
          .catch(err => logger.error('Cache invalidation error:', err));
      }
      
      return originalSend.call(this, data);
    };

    next();
  };
};

// Preload cache for critical data
const preloadCache = async (userId, farmId) => {
  const redis = getRedisClient();
  if (!redis) return;

  try {
    // Preload dashboard data
    const dashboardKey = `medium:${userId}:${farmId}:/api/v1/farms/${farmId}/dashboard/summary`;
    // This would typically fetch from database and cache
    logger.info(`Preloading cache for user ${userId}, farm ${farmId}`);
  } catch (error) {
    logger.error('Cache preload error:', error);
  }
};

// Cache warming job
const warmCache = async () => {
  try {
    // Get active users and farms
    const User = require('../models/User');
    const Farm = require('../models/Farm');
    
    const users = await User.find({ lastLogin: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } })
      .populate('farms')
      .limit(100);

    for (const user of users) {
      for (const farm of user.farms) {
        await preloadCache(user._id, farm._id);
      }
    }
    
    logger.info('Cache warming completed');
  } catch (error) {
    logger.error('Cache warming error:', error);
  }
};

// Cache statistics
const getCacheStats = async () => {
  const redis = getRedisClient();
  if (!redis) return { error: 'Redis not available' };

  try {
    const info = await redis.info('memory');
    const keyspace = await redis.info('keyspace');
    
    return {
      memory: info,
      keyspace: keyspace,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    logger.error('Cache stats error:', error);
    return { error: error.message };
  }
};

module.exports = {
  cache: cacheConfigs,
  invalidateCache,
  invalidateCacheOnWrite,
  preloadCache,
  warmCache,
  getCacheStats
};
