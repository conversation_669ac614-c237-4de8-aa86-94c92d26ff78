const promClient = require('prom-client');

// Create a Registry to register the metrics
const register = new promClient.Registry();

// Add a default label which is added to all metrics
register.setDefaultLabels({
  app: 'smartpoultry-backend',
  environment: process.env.NODE_ENV || 'development'
});

// Enable the collection of default metrics
promClient.collectDefaultMetrics({ register });

// Create custom metrics
const httpRequestDuration = new promClient.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
});

const httpRequestsTotal = new promClient.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

const activeConnections = new promClient.Gauge({
  name: 'active_connections',
  help: 'Number of active connections'
});

const databaseConnections = new promClient.Gauge({
  name: 'database_connections',
  help: 'Number of database connections',
  labelNames: ['state']
});

const redisConnections = new promClient.Gauge({
  name: 'redis_connections',
  help: 'Number of Redis connections',
  labelNames: ['state']
});

const apiErrors = new promClient.Counter({
  name: 'api_errors_total',
  help: 'Total number of API errors',
  labelNames: ['type', 'endpoint']
});

const businessMetrics = {
  totalUsers: new promClient.Gauge({
    name: 'total_users',
    help: 'Total number of registered users'
  }),
  totalFarms: new promClient.Gauge({
    name: 'total_farms',
    help: 'Total number of farms'
  }),
  totalChickens: new promClient.Gauge({
    name: 'total_chickens',
    help: 'Total number of chickens'
  }),
  dailyEggProduction: new promClient.Gauge({
    name: 'daily_egg_production',
    help: 'Daily egg production count'
  })
};

// Register all metrics
register.registerMetric(httpRequestDuration);
register.registerMetric(httpRequestsTotal);
register.registerMetric(activeConnections);
register.registerMetric(databaseConnections);
register.registerMetric(redisConnections);
register.registerMetric(apiErrors);

Object.values(businessMetrics).forEach(metric => {
  register.registerMetric(metric);
});

// Middleware to collect HTTP metrics
const collectHttpMetrics = (req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    const route = req.route ? req.route.path : req.path;
    
    httpRequestDuration
      .labels(req.method, route, res.statusCode)
      .observe(duration);
    
    httpRequestsTotal
      .labels(req.method, route, res.statusCode)
      .inc();
  });
  
  next();
};

// Function to update business metrics
const updateBusinessMetrics = async () => {
  try {
    const User = require('../models/User');
    const Farm = require('../models/Farm');
    const Chicken = require('../models/Chicken');
    const EggProduction = require('../models/EggProduction');
    
    // Update user count
    const userCount = await User.countDocuments();
    businessMetrics.totalUsers.set(userCount);
    
    // Update farm count
    const farmCount = await Farm.countDocuments();
    businessMetrics.totalFarms.set(farmCount);
    
    // Update chicken count
    const chickenCount = await Chicken.countDocuments();
    businessMetrics.totalChickens.set(chickenCount);
    
    // Update daily egg production
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const dailyEggs = await EggProduction.aggregate([
      {
        $match: {
          date: { $gte: today, $lt: tomorrow }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$quantity' }
        }
      }
    ]);
    
    const eggCount = dailyEggs.length > 0 ? dailyEggs[0].total : 0;
    businessMetrics.dailyEggProduction.set(eggCount);
    
  } catch (error) {
    console.error('Error updating business metrics:', error);
  }
};

// Update business metrics every 5 minutes
setInterval(updateBusinessMetrics, 5 * 60 * 1000);

module.exports = {
  register,
  collectHttpMetrics,
  updateBusinessMetrics,
  metrics: {
    httpRequestDuration,
    httpRequestsTotal,
    activeConnections,
    databaseConnections,
    redisConnections,
    apiErrors,
    ...businessMetrics
  }
};
