# Multi-stage production Dockerfile for backend
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies including dev dependencies for building
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Remove dev dependencies and unnecessary files
RUN rm -rf tests/ docs/ *.md

FROM node:18-alpine AS production

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S smartpoultry -u 1001

WORKDIR /app

# Copy built application from builder stage
COPY --from=builder --chown=smartpoultry:nodejs /app .

# Install dumb-init for proper signal handling
RUN apk add --no-cache dumb-init

# Switch to non-root user
USER smartpoultry

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js

EXPOSE 5000

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]
<PERSON><PERSON> ["node", "src/server.js"]
