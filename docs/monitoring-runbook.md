# SmartPoultry Monitoring and Alerting Runbook

This runbook provides operational procedures for monitoring, alerting, and incident response for the SmartPoultry production environment.

## Overview

SmartPoultry uses a comprehensive monitoring stack:

- **Prometheus**: Metrics collection and alerting
- **Grafana**: Visualization and dashboards
- **ELK Stack**: Log aggregation and analysis
- **Sentry**: Error tracking and performance monitoring
- **Kubernetes**: Native monitoring and health checks

## Monitoring Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │───▶│   Prometheus    │───▶│     Grafana     │
│   (Metrics)     │    │   (Collection)  │    │ (Visualization) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │───▶│    Logstash     │───▶│   Kibana/ES     │
│     (Logs)      │    │  (Processing)   │    │   (Analysis)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │───▶│     Sentry      │───▶│   Alerting      │
│    (Errors)     │    │ (Error Tracking)│    │   (Slack/Email) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Key Metrics and Thresholds

### Application Metrics

| Metric | Threshold | Severity | Action |
|--------|-----------|----------|--------|
| Response Time (95th percentile) | > 2s | Warning | Investigate performance |
| Response Time (95th percentile) | > 5s | Critical | Immediate investigation |
| Error Rate | > 5% | Warning | Check logs and errors |
| Error Rate | > 10% | Critical | Immediate response |
| Request Rate | < 10% of baseline | Warning | Check traffic sources |
| Active Users | < 50% of baseline | Critical | System health check |

### Infrastructure Metrics

| Metric | Threshold | Severity | Action |
|--------|-----------|----------|--------|
| CPU Usage | > 80% | Warning | Monitor for scaling |
| CPU Usage | > 90% | Critical | Scale immediately |
| Memory Usage | > 85% | Warning | Monitor for leaks |
| Memory Usage | > 95% | Critical | Restart/scale pods |
| Disk Usage | > 80% | Warning | Plan cleanup |
| Disk Usage | > 90% | Critical | Immediate cleanup |
| Pod Restart Count | > 5 in 1h | Warning | Investigate cause |
| Pod Restart Count | > 10 in 1h | Critical | Immediate investigation |

### Database Metrics

| Metric | Threshold | Severity | Action |
|--------|-----------|----------|--------|
| Connection Count | > 80% of max | Warning | Monitor connections |
| Connection Count | > 95% of max | Critical | Scale/restart |
| Query Response Time | > 1s | Warning | Optimize queries |
| Query Response Time | > 3s | Critical | Immediate optimization |
| Replication Lag | > 10s | Warning | Check replica health |
| Replication Lag | > 30s | Critical | Investigate replication |

### Business Metrics

| Metric | Threshold | Severity | Action |
|--------|-----------|----------|--------|
| User Registration Rate | < 50% of baseline | Warning | Check user flow |
| Farm Creation Rate | < 50% of baseline | Warning | Investigate issues |
| API Usage | < 30% of baseline | Critical | System health check |
| Failed Logins | > 20% | Warning | Check auth system |

## Dashboards

### 1. Application Overview Dashboard

**URL**: `https://grafana.your-domain.com/d/app-overview`

**Panels**:
- Request rate and response time
- Error rate and status code distribution
- Active users and sessions
- Top endpoints by traffic
- Geographic distribution of users

### 2. Infrastructure Dashboard

**URL**: `https://grafana.your-domain.com/d/infrastructure`

**Panels**:
- Cluster resource utilization
- Pod status and restart counts
- Node health and capacity
- Network traffic and latency
- Storage usage and IOPS

### 3. Database Dashboard

**URL**: `https://grafana.your-domain.com/d/database`

**Panels**:
- Connection pool status
- Query performance metrics
- Index usage and efficiency
- Replication status
- Storage and backup status

### 4. Business Metrics Dashboard

**URL**: `https://grafana.your-domain.com/d/business`

**Panels**:
- User registration trends
- Farm and chicken counts
- Egg production metrics
- Revenue and financial trends
- Feature usage analytics

## Alert Rules

### Critical Alerts (Immediate Response Required)

#### Application Down
```yaml
alert: ApplicationDown
expr: up{job="smartpoultry-backend"} == 0
for: 1m
labels:
  severity: critical
annotations:
  summary: "SmartPoultry application is down"
  description: "The SmartPoultry backend has been down for more than 1 minute"
```

#### High Error Rate
```yaml
alert: HighErrorRate
expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.1
for: 5m
labels:
  severity: critical
annotations:
  summary: "High error rate detected"
  description: "Error rate is {{ $value | humanizePercentage }} for the last 5 minutes"
```

#### Database Connection Failure
```yaml
alert: DatabaseConnectionFailure
expr: mongodb_connections{state="current"} == 0
for: 2m
labels:
  severity: critical
annotations:
  summary: "Database connection failure"
  description: "No active database connections detected"
```

### Warning Alerts (Investigation Required)

#### High Response Time
```yaml
alert: HighResponseTime
expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
for: 10m
labels:
  severity: warning
annotations:
  summary: "High response time detected"
  description: "95th percentile response time is {{ $value }}s"
```

#### High Memory Usage
```yaml
alert: HighMemoryUsage
expr: (container_memory_usage_bytes / container_spec_memory_limit_bytes) > 0.85
for: 15m
labels:
  severity: warning
annotations:
  summary: "High memory usage"
  description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.pod }}"
```

## Incident Response Procedures

### Severity Levels

#### Severity 1 (Critical)
- **Definition**: Complete system outage or data loss
- **Response Time**: Immediate (< 15 minutes)
- **Escalation**: Automatic to on-call engineer
- **Communication**: Status page update within 30 minutes

#### Severity 2 (High)
- **Definition**: Major feature unavailable or significant performance degradation
- **Response Time**: 2 hours
- **Escalation**: To team lead if not resolved in 4 hours
- **Communication**: Internal notification

#### Severity 3 (Medium)
- **Definition**: Minor feature issues or performance degradation
- **Response Time**: 24 hours
- **Escalation**: To team lead if not resolved in 48 hours
- **Communication**: Ticket tracking

#### Severity 4 (Low)
- **Definition**: Cosmetic issues or minor bugs
- **Response Time**: Next business day
- **Escalation**: Standard development process
- **Communication**: Development backlog

### Response Workflow

1. **Alert Received**
   - Acknowledge alert within 5 minutes
   - Begin initial investigation
   - Update incident status

2. **Initial Assessment**
   - Determine severity level
   - Identify affected systems/users
   - Estimate impact and scope

3. **Investigation**
   - Check monitoring dashboards
   - Review recent deployments
   - Analyze logs and metrics
   - Identify root cause

4. **Resolution**
   - Implement fix or workaround
   - Verify resolution
   - Monitor for recurrence

5. **Post-Incident**
   - Document incident details
   - Conduct post-mortem if Severity 1-2
   - Update runbooks and procedures

## Common Issues and Solutions

### Application Issues

#### High Response Time
**Symptoms**: Slow API responses, user complaints
**Investigation**:
```bash
# Check application metrics
kubectl top pods -n smartpoultry
kubectl logs -f deployment/backend -n smartpoultry

# Check database performance
kubectl exec -it deployment/mongodb -n smartpoultry -- mongostat
```
**Solutions**:
- Scale backend pods: `kubectl scale deployment backend --replicas=5 -n smartpoultry`
- Check for slow queries in database
- Review recent code changes

#### Memory Leaks
**Symptoms**: Increasing memory usage, pod restarts
**Investigation**:
```bash
# Check memory usage trends
kubectl top pods -n smartpoultry
kubectl describe pod <pod-name> -n smartpoultry

# Check for memory leaks in application
kubectl exec -it <pod-name> -n smartpoultry -- node --inspect
```
**Solutions**:
- Restart affected pods
- Review application code for memory leaks
- Increase memory limits temporarily

### Database Issues

#### Connection Pool Exhaustion
**Symptoms**: Database connection errors, timeouts
**Investigation**:
```bash
# Check connection count
kubectl exec -it deployment/mongodb -n smartpoultry -- mongosh --eval "db.serverStatus().connections"

# Check application connection pool
kubectl logs deployment/backend -n smartpoultry | grep "connection"
```
**Solutions**:
- Increase connection pool size
- Check for connection leaks in application
- Scale database if needed

#### Slow Queries
**Symptoms**: High database response time, query timeouts
**Investigation**:
```bash
# Enable profiling
kubectl exec -it deployment/mongodb -n smartpoultry -- mongosh --eval "db.setProfilingLevel(2)"

# Check slow queries
kubectl exec -it deployment/mongodb -n smartpoultry -- mongosh --eval "db.system.profile.find().sort({ts:-1}).limit(5)"
```
**Solutions**:
- Add missing indexes
- Optimize query patterns
- Consider query caching

### Infrastructure Issues

#### Pod Crashes
**Symptoms**: Pod restart loops, application unavailability
**Investigation**:
```bash
# Check pod status
kubectl get pods -n smartpoultry
kubectl describe pod <pod-name> -n smartpoultry
kubectl logs <pod-name> -n smartpoultry --previous
```
**Solutions**:
- Check resource limits
- Review application logs for errors
- Verify configuration and secrets

#### Node Issues
**Symptoms**: Pods stuck in pending state, node not ready
**Investigation**:
```bash
# Check node status
kubectl get nodes
kubectl describe node <node-name>
kubectl top nodes
```
**Solutions**:
- Drain and restart problematic nodes
- Check node resource availability
- Scale cluster if needed

## Log Analysis

### Application Logs

**Location**: Elasticsearch via Kibana
**URL**: `https://kibana.your-domain.com`

**Common Queries**:
```
# Error logs in last hour
level:error AND @timestamp:[now-1h TO now]

# Slow requests
duration:>2000 AND @timestamp:[now-1h TO now]

# Authentication failures
message:"authentication failed" AND @timestamp:[now-1h TO now]

# Database errors
message:"database" AND level:error AND @timestamp:[now-1h TO now]
```

### System Logs

**Access via kubectl**:
```bash
# Application logs
kubectl logs -f deployment/backend -n smartpoultry
kubectl logs -f deployment/frontend -n smartpoultry

# Database logs
kubectl logs -f deployment/mongodb -n smartpoultry

# Infrastructure logs
kubectl logs -f deployment/prometheus -n smartpoultry
```

## Performance Optimization

### Monitoring Performance Trends

1. **Daily Review**
   - Check response time trends
   - Review error rate patterns
   - Monitor resource utilization

2. **Weekly Analysis**
   - Analyze user behavior patterns
   - Review capacity planning metrics
   - Check for performance regressions

3. **Monthly Optimization**
   - Database query optimization
   - Index analysis and cleanup
   - Resource allocation review

### Capacity Planning

**Metrics to Track**:
- User growth rate
- Resource utilization trends
- Database growth patterns
- Network traffic patterns

**Scaling Triggers**:
- CPU usage > 70% for 24 hours
- Memory usage > 80% for 24 hours
- Response time > 1.5s for 95th percentile
- Error rate > 2% for 24 hours

## Maintenance Windows

### Scheduled Maintenance

**Frequency**: Monthly (first Sunday of each month, 2-6 AM UTC)

**Procedures**:
1. Notify users 48 hours in advance
2. Scale down non-critical services
3. Perform updates and maintenance
4. Run health checks
5. Scale services back up
6. Monitor for issues

### Emergency Maintenance

**Triggers**:
- Critical security vulnerabilities
- Data corruption issues
- Major system failures

**Procedures**:
1. Assess urgency and impact
2. Notify stakeholders immediately
3. Implement emergency procedures
4. Monitor and verify fixes
5. Conduct post-incident review

## Contact Information

### On-Call Rotation

- **Primary**: Current on-call engineer (see PagerDuty)
- **Secondary**: Backup on-call engineer
- **Escalation**: Engineering Manager

### Communication Channels

- **Slack**: #smartpoultry-alerts (automated alerts)
- **Slack**: #smartpoultry-incidents (incident coordination)
- **Email**: <EMAIL>
- **Phone**: +1-xxx-xxx-xxxx (emergency only)

### External Services

- **Status Page**: https://status.smartpoultry.com
- **Monitoring**: https://grafana.your-domain.com
- **Logs**: https://kibana.your-domain.com
- **Error Tracking**: https://sentry.io/smartpoultry

This runbook should be reviewed and updated monthly to ensure accuracy and completeness.
