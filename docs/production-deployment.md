# SmartPoultry Production Deployment Guide

This guide provides comprehensive instructions for deploying SmartPoultry to production environments.

## Prerequisites

### Infrastructure Requirements

- **Kubernetes Cluster**: v1.25+ with at least 3 nodes
- **Node Resources**: Minimum 4 CPU cores, 8GB RAM per node
- **Storage**: 100GB+ SSD storage with backup capabilities
- **Network**: Load balancer with SSL termination
- **Domain**: Registered domain with DNS management

### Required Tools

- `kubectl` v1.25+
- `helm` v3.10+
- `docker` v20.10+
- `aws-cli` v2.0+ (if using AWS)

### Required Accounts & Services

- Container registry (GitHub Container Registry, AWS ECR, etc.)
- Cloud storage for backups (AWS S3, Google Cloud Storage, etc.)
- Monitoring service (optional: Datadog, New Relic)
- Error tracking (Sentry)
- Email service (SendGrid, AWS SES)

## Environment Setup

### 1. Clone Repository

```bash
git clone https://github.com/your-org/SmartPoultry.git
cd SmartPoultry
```

### 2. Configure Environment Variables

Create production environment files:

```bash
# Copy and customize environment files
cp .env.production .env
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env
```

Update the following critical variables:

```bash
# Database
MONGO_ROOT_PASSWORD=<strong-password>
MONGO_PASSWORD=<strong-password>

# Security
JWT_SECRET=<64-character-random-string>
REDIS_PASSWORD=<strong-password>

# External Services
GEMINI_API_KEY=<your-gemini-api-key>
SENTRY_DSN=<your-sentry-dsn>

# Domain
CLIENT_URL=https://your-domain.com

# AWS (for backups)
AWS_ACCESS_KEY_ID=<your-aws-key>
AWS_SECRET_ACCESS_KEY=<your-aws-secret>
AWS_S3_BUCKET=<your-backup-bucket>
```

### 3. Generate Kubernetes Secrets

```bash
# Create namespace
kubectl create namespace smartpoultry

# Create secrets from environment variables
kubectl create secret generic smartpoultry-secrets \
  --from-literal=MONGO_ROOT_USERNAME=admin \
  --from-literal=MONGO_ROOT_PASSWORD=<password> \
  --from-literal=MONGO_USERNAME=smartpoultry_user \
  --from-literal=MONGO_PASSWORD=<password> \
  --from-literal=REDIS_PASSWORD=<password> \
  --from-literal=JWT_SECRET=<jwt-secret> \
  --from-literal=GEMINI_API_KEY=<api-key> \
  --from-literal=SENTRY_DSN=<sentry-dsn> \
  --namespace smartpoultry

# Create AWS secrets for backups
kubectl create secret generic aws-secrets \
  --from-literal=access-key-id=<aws-key> \
  --from-literal=secret-access-key=<aws-secret> \
  --namespace smartpoultry
```

## Deployment Steps

### 1. Deploy Infrastructure Components

```bash
# Deploy in order
kubectl apply -f k8s/namespace.yaml
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/secrets.yaml
kubectl apply -f k8s/mongodb-init-configmap.yaml
```

### 2. Deploy Database Layer

```bash
# Deploy MongoDB
kubectl apply -f k8s/mongodb-deployment.yaml

# Deploy Redis
kubectl apply -f k8s/redis-deployment.yaml

# Wait for databases to be ready
kubectl wait --for=condition=ready pod -l app=mongodb -n smartpoultry --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n smartpoultry --timeout=300s
```

### 3. Deploy Application Layer

```bash
# Deploy backend
kubectl apply -f k8s/backend-deployment.yaml

# Deploy frontend
kubectl apply -f k8s/frontend-deployment.yaml

# Wait for applications to be ready
kubectl wait --for=condition=ready pod -l app=backend -n smartpoultry --timeout=300s
kubectl wait --for=condition=ready pod -l app=frontend -n smartpoultry --timeout=300s
```

### 4. Deploy Monitoring Stack

```bash
# Deploy Prometheus
kubectl apply -f monitoring/prometheus-deployment.yaml

# Deploy Grafana
kubectl apply -f monitoring/grafana-deployment.yaml

# Deploy ELK Stack
kubectl apply -f monitoring/elasticsearch-deployment.yaml
kubectl apply -f monitoring/kibana-deployment.yaml
kubectl apply -f monitoring/logstash-deployment.yaml
```

### 5. Configure Ingress and SSL

```bash
# Install cert-manager (if not already installed)
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml

# Deploy ingress with SSL
kubectl apply -f k8s/ingress.yaml
```

### 6. Setup Backup System

```bash
# Deploy backup CronJob
kubectl apply -f k8s/backup-cronjob.yaml

# Test backup manually
kubectl create job --from=cronjob/mongodb-backup manual-backup-test -n smartpoultry
```

## Post-Deployment Verification

### 1. Health Checks

```bash
# Check all pods are running
kubectl get pods -n smartpoultry

# Check services
kubectl get services -n smartpoultry

# Check ingress
kubectl get ingress -n smartpoultry

# Test application endpoints
curl https://your-domain.com/health
curl https://api.your-domain.com/health
```

### 2. Database Verification

```bash
# Connect to MongoDB
kubectl exec -it deployment/mongodb -n smartpoultry -- mongosh

# Verify database and user creation
use smartpoultry_prod
db.auth("smartpoultry_user", "your-password")
show collections
```

### 3. Monitoring Setup

```bash
# Access Grafana
kubectl port-forward service/grafana-service 3000:3000 -n smartpoultry

# Access Prometheus
kubectl port-forward service/prometheus-service 9090:9090 -n smartpoultry

# Access Kibana
kubectl port-forward service/kibana-service 5601:5601 -n smartpoultry
```

## Scaling Configuration

### Horizontal Pod Autoscaling

The deployment includes HPA configurations:

- **Backend**: 2-10 replicas based on CPU (70%) and memory (80%)
- **Frontend**: 2-5 replicas based on CPU (70%)

### Vertical Scaling

To increase resources for individual pods:

```bash
# Edit deployment
kubectl edit deployment backend -n smartpoultry

# Update resource limits
resources:
  requests:
    memory: "512Mi"
    cpu: "400m"
  limits:
    memory: "1Gi"
    cpu: "800m"
```

## Backup and Recovery

### Automated Backups

Backups run daily at 2 AM UTC via CronJob:

- MongoDB dump with compression
- Upload to AWS S3
- 30-day retention policy
- Slack notifications on success/failure

### Manual Backup

```bash
# Create manual backup
kubectl create job --from=cronjob/mongodb-backup manual-backup -n smartpoultry

# Check backup status
kubectl logs job/manual-backup -n smartpoultry
```

### Disaster Recovery

```bash
# List available backups
aws s3 ls s3://your-backup-bucket/mongodb-backups/

# Restore from backup
kubectl run mongodb-restore --rm -i --tty \
  --image=smartpoultry/backup:latest \
  --env="MONGO_HOST=mongodb-service" \
  --env="MONGO_DB_NAME=smartpoultry_prod" \
  --env="AWS_S3_BUCKET=your-backup-bucket" \
  -- /scripts/restore-production.sh --s3 backup_file.tar.gz --drop
```

## Security Considerations

### Network Security

- All inter-service communication within cluster
- Ingress controller with SSL termination
- Network policies to restrict pod-to-pod communication
- Regular security scanning with Trivy

### Data Security

- Encrypted secrets in Kubernetes
- Database authentication enabled
- Regular password rotation
- Audit logging enabled

### Application Security

- Rate limiting on all endpoints
- Input sanitization and validation
- CORS properly configured
- Security headers enforced

## Monitoring and Alerting

### Key Metrics to Monitor

- **Application**: Response time, error rate, throughput
- **Infrastructure**: CPU, memory, disk usage
- **Database**: Connection count, query performance
- **Business**: User registrations, farm creation, egg production

### Alert Configuration

Set up alerts for:

- Pod restart loops
- High error rates (>5%)
- Database connection failures
- Disk space usage (>80%)
- Memory usage (>85%)
- Failed backups

## Troubleshooting

### Common Issues

1. **Pod CrashLoopBackOff**
   ```bash
   kubectl describe pod <pod-name> -n smartpoultry
   kubectl logs <pod-name> -n smartpoultry --previous
   ```

2. **Database Connection Issues**
   ```bash
   kubectl exec -it deployment/backend -n smartpoultry -- env | grep MONGO
   kubectl exec -it deployment/mongodb -n smartpoultry -- mongosh --eval "db.adminCommand('ping')"
   ```

3. **SSL Certificate Issues**
   ```bash
   kubectl describe certificate smartpoultry-tls -n smartpoultry
   kubectl describe certificaterequest -n smartpoultry
   ```

### Log Analysis

```bash
# Application logs
kubectl logs -f deployment/backend -n smartpoultry
kubectl logs -f deployment/frontend -n smartpoultry

# Database logs
kubectl logs -f deployment/mongodb -n smartpoultry

# Ingress logs
kubectl logs -f deployment/nginx-ingress-controller -n ingress-nginx
```

## Maintenance

### Regular Tasks

- **Weekly**: Review monitoring dashboards and alerts
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Review and test disaster recovery procedures
- **Annually**: Security audit and penetration testing

### Updates and Rollbacks

```bash
# Update application
kubectl set image deployment/backend backend=smartpoultry/backend:v2.0.0 -n smartpoultry

# Check rollout status
kubectl rollout status deployment/backend -n smartpoultry

# Rollback if needed
kubectl rollout undo deployment/backend -n smartpoultry
```

## Performance Optimization

### Database Optimization

- Regular index analysis and optimization
- Connection pooling configuration
- Query performance monitoring
- Automated cleanup of old data

### Application Optimization

- CDN for static assets
- Redis caching for frequently accessed data
- Image optimization and compression
- Code splitting and lazy loading

### Infrastructure Optimization

- Node autoscaling based on demand
- Spot instances for non-critical workloads
- Resource requests and limits tuning
- Network optimization

## Support and Escalation

### Incident Response

1. **Severity 1** (System Down): Immediate response required
2. **Severity 2** (Major Feature Down): 2-hour response time
3. **Severity 3** (Minor Issues): 24-hour response time

### Contact Information

- **DevOps Team**: <EMAIL>
- **On-call Engineer**: +1-xxx-xxx-xxxx
- **Slack Channel**: #smartpoultry-alerts

### Escalation Matrix

1. On-call Engineer
2. DevOps Lead
3. Engineering Manager
4. CTO

This deployment guide should be reviewed and updated regularly to reflect changes in infrastructure, security requirements, and operational procedures.
