# SmartPoultry Disaster Recovery Plan

This document outlines the disaster recovery procedures for the SmartPoultry application to ensure business continuity in case of system failures, data loss, or other catastrophic events.

## Overview

### Recovery Objectives

- **Recovery Time Objective (RTO)**: 4 hours maximum downtime
- **Recovery Point Objective (RPO)**: 1 hour maximum data loss
- **Availability Target**: 99.9% uptime (8.76 hours downtime per year)

### Disaster Scenarios

1. **Database Failure**: MongoDB corruption or complete loss
2. **Application Failure**: Backend/frontend service unavailability
3. **Infrastructure Failure**: Kubernetes cluster or node failures
4. **Data Center Outage**: Complete regional failure
5. **Security Breach**: Compromised systems requiring rebuild
6. **Human Error**: Accidental deletion or misconfiguration

## Backup Strategy

### Automated Backups

#### Database Backups
- **Frequency**: Daily at 2:00 AM UTC
- **Retention**: 30 days
- **Storage**: AWS S3 with cross-region replication
- **Encryption**: AES-256 encryption at rest and in transit
- **Verification**: Automated restore testing weekly

#### Application Backups
- **Container Images**: Stored in GitHub Container Registry
- **Configuration**: Kubernetes manifests in Git repository
- **Secrets**: Encrypted and stored in secure vault
- **Monitoring Data**: Prometheus data retained for 30 days

#### Infrastructure Backups
- **Kubernetes State**: etcd snapshots daily
- **Persistent Volumes**: Snapshot-based backups
- **Network Configuration**: Infrastructure as Code in Git

### Backup Verification

```bash
# Weekly backup verification script
#!/bin/bash
BACKUP_DATE=$(date -d "yesterday" +%Y%m%d)
BACKUP_FILE="smartpoultry_prod_${BACKUP_DATE}_*.tar.gz"

# Download latest backup
aws s3 cp s3://smartpoultry-backups/mongodb-backups/${BACKUP_FILE} /tmp/

# Test restore to temporary database
kubectl run backup-test --rm -i --tty \
  --image=smartpoultry/backup:latest \
  --env="MONGO_HOST=mongodb-test" \
  --env="MONGO_DB_NAME=smartpoultry_test" \
  -- /scripts/restore-production.sh --file /tmp/${BACKUP_FILE}

# Verify data integrity
kubectl exec -it backup-test -- mongosh smartpoultry_test --eval "
  db.users.countDocuments();
  db.farms.countDocuments();
  db.chickens.countDocuments();
"
```

## Recovery Procedures

### Scenario 1: Database Failure

#### Symptoms
- Database connection errors
- Data corruption alerts
- MongoDB pod crash loops

#### Recovery Steps

1. **Assess Damage**
   ```bash
   # Check database status
   kubectl get pods -l app=mongodb -n smartpoultry
   kubectl logs deployment/mongodb -n smartpoultry
   
   # Check data integrity
   kubectl exec -it deployment/mongodb -n smartpoultry -- mongosh --eval "
     db.runCommand({dbStats: 1});
     db.runCommand({collStats: 'users'});
   "
   ```

2. **Stop Application Services**
   ```bash
   # Scale down backend to prevent further data corruption
   kubectl scale deployment backend --replicas=0 -n smartpoultry
   ```

3. **Restore from Backup**
   ```bash
   # List available backups
   aws s3 ls s3://smartpoultry-backups/mongodb-backups/ | tail -10
   
   # Select most recent backup
   BACKUP_FILE="smartpoultry_prod_20240803_020000.tar.gz"
   
   # Restore database
   kubectl run mongodb-restore --rm -i --tty \
     --image=smartpoultry/backup:latest \
     --env="MONGO_HOST=mongodb-service" \
     --env="MONGO_DB_NAME=smartpoultry_prod" \
     --env="AWS_S3_BUCKET=smartpoultry-backups" \
     -- /scripts/restore-production.sh --s3 ${BACKUP_FILE} --drop
   ```

4. **Verify Restoration**
   ```bash
   # Check database health
   kubectl exec -it deployment/mongodb -n smartpoultry -- mongosh smartpoultry_prod --eval "
     db.runCommand({ping: 1});
     db.users.countDocuments();
     db.farms.countDocuments();
   "
   ```

5. **Restart Services**
   ```bash
   # Scale backend back up
   kubectl scale deployment backend --replicas=3 -n smartpoultry
   
   # Verify application health
   kubectl wait --for=condition=ready pod -l app=backend -n smartpoultry --timeout=300s
   curl https://api.smartpoultry.com/health
   ```

**Estimated Recovery Time**: 2-3 hours

### Scenario 2: Complete Infrastructure Failure

#### Symptoms
- Entire Kubernetes cluster unavailable
- All services unreachable
- Infrastructure monitoring alerts

#### Recovery Steps

1. **Activate Secondary Region**
   ```bash
   # Switch to backup cluster
   kubectl config use-context backup-cluster
   
   # Verify cluster status
   kubectl get nodes
   kubectl get namespaces
   ```

2. **Deploy Core Infrastructure**
   ```bash
   # Create namespace and secrets
   kubectl apply -f k8s/namespace.yaml
   kubectl apply -f k8s/secrets.yaml
   kubectl apply -f k8s/configmap.yaml
   
   # Deploy databases
   kubectl apply -f k8s/mongodb-deployment.yaml
   kubectl apply -f k8s/redis-deployment.yaml
   ```

3. **Restore Database**
   ```bash
   # Wait for MongoDB to be ready
   kubectl wait --for=condition=ready pod -l app=mongodb -n smartpoultry --timeout=600s
   
   # Restore from latest backup
   LATEST_BACKUP=$(aws s3 ls s3://smartpoultry-backups/mongodb-backups/ | tail -1 | awk '{print $4}')
   
   kubectl run mongodb-restore --rm -i --tty \
     --image=smartpoultry/backup:latest \
     --env="MONGO_HOST=mongodb-service" \
     --env="MONGO_DB_NAME=smartpoultry_prod" \
     --env="AWS_S3_BUCKET=smartpoultry-backups" \
     -- /scripts/restore-production.sh --s3 ${LATEST_BACKUP}
   ```

4. **Deploy Applications**
   ```bash
   # Deploy backend and frontend
   kubectl apply -f k8s/backend-deployment.yaml
   kubectl apply -f k8s/frontend-deployment.yaml
   
   # Deploy monitoring
   kubectl apply -f monitoring/
   ```

5. **Update DNS**
   ```bash
   # Update DNS to point to new cluster
   aws route53 change-resource-record-sets \
     --hosted-zone-id Z123456789 \
     --change-batch file://dns-failover.json
   ```

6. **Verify Full Recovery**
   ```bash
   # Test all endpoints
   curl https://smartpoultry.com/health
   curl https://api.smartpoultry.com/health
   
   # Test user functionality
   curl -X POST https://api.smartpoultry.com/api/v1/auth/login \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"testpass"}'
   ```

**Estimated Recovery Time**: 3-4 hours

### Scenario 3: Security Breach

#### Immediate Response

1. **Isolate Affected Systems**
   ```bash
   # Block all external traffic
   kubectl patch ingress smartpoultry-ingress -n smartpoultry \
     --type='json' -p='[{"op": "replace", "path": "/spec/rules", "value": []}]'
   
   # Scale down all services
   kubectl scale deployment --all --replicas=0 -n smartpoultry
   ```

2. **Preserve Evidence**
   ```bash
   # Capture logs
   kubectl logs deployment/backend -n smartpoultry > incident-backend-logs.txt
   kubectl logs deployment/frontend -n smartpoultry > incident-frontend-logs.txt
   
   # Export pod descriptions
   kubectl describe pods -n smartpoultry > incident-pod-descriptions.txt
   ```

3. **Assess Breach Scope**
   - Review access logs
   - Check for data exfiltration
   - Identify compromised accounts
   - Analyze attack vectors

#### Recovery Steps

1. **Rebuild Infrastructure**
   ```bash
   # Delete compromised namespace
   kubectl delete namespace smartpoultry
   
   # Recreate from clean state
   kubectl apply -f k8s/namespace.yaml
   ```

2. **Rotate All Secrets**
   ```bash
   # Generate new secrets
   NEW_JWT_SECRET=$(openssl rand -base64 64)
   NEW_MONGO_PASSWORD=$(openssl rand -base64 32)
   NEW_REDIS_PASSWORD=$(openssl rand -base64 32)
   
   # Update secrets
   kubectl create secret generic smartpoultry-secrets \
     --from-literal=JWT_SECRET=${NEW_JWT_SECRET} \
     --from-literal=MONGO_PASSWORD=${NEW_MONGO_PASSWORD} \
     --from-literal=REDIS_PASSWORD=${NEW_REDIS_PASSWORD} \
     --namespace smartpoultry
   ```

3. **Deploy Clean Images**
   ```bash
   # Use verified clean container images
   kubectl apply -f k8s/ --namespace smartpoultry
   ```

4. **Restore Data with Verification**
   ```bash
   # Restore from backup before breach
   CLEAN_BACKUP="smartpoultry_prod_20240802_020000.tar.gz"
   
   kubectl run mongodb-restore --rm -i --tty \
     --image=smartpoultry/backup:latest \
     --env="MONGO_HOST=mongodb-service" \
     --env="MONGO_DB_NAME=smartpoultry_prod" \
     --env="AWS_S3_BUCKET=smartpoultry-backups" \
     -- /scripts/restore-production.sh --s3 ${CLEAN_BACKUP} --drop
   ```

5. **Security Hardening**
   - Update all dependencies
   - Apply security patches
   - Review and update access controls
   - Implement additional monitoring

**Estimated Recovery Time**: 6-8 hours

## Communication Plan

### Internal Communication

#### Incident Declaration
1. **Immediate**: Slack #smartpoultry-incidents
2. **Within 15 minutes**: Email to engineering team
3. **Within 30 minutes**: Notification to management
4. **Within 1 hour**: Status page update

#### Regular Updates
- Every 30 minutes during active incident
- Include current status, actions taken, next steps
- Estimate time to resolution

### External Communication

#### Customer Notification
1. **Status Page**: Update within 30 minutes
2. **Email**: Send to affected customers within 2 hours
3. **Social Media**: Post updates if widespread impact

#### Template Messages

**Initial Notification**:
```
We are currently experiencing technical difficulties with SmartPoultry. 
Our team is actively working to resolve the issue. We will provide 
updates every 30 minutes. We apologize for any inconvenience.
```

**Resolution Notification**:
```
The technical issues with SmartPoultry have been resolved. All services 
are now operating normally. We apologize for the disruption and thank 
you for your patience.
```

## Testing and Validation

### Disaster Recovery Testing

#### Monthly Tests
- Database backup and restore verification
- Application deployment from scratch
- Monitoring system recovery

#### Quarterly Tests
- Full infrastructure failover
- Cross-region disaster recovery
- Security incident response simulation

#### Annual Tests
- Complete disaster recovery exercise
- Business continuity validation
- Third-party security assessment

### Test Procedures

#### Database Recovery Test
```bash
#!/bin/bash
# Monthly database recovery test

# Create test environment
kubectl create namespace smartpoultry-dr-test

# Deploy minimal infrastructure
kubectl apply -f k8s/mongodb-deployment.yaml -n smartpoultry-dr-test

# Restore from backup
BACKUP_FILE=$(aws s3 ls s3://smartpoultry-backups/mongodb-backups/ | tail -1 | awk '{print $4}')
kubectl run mongodb-restore --rm -i --tty \
  --image=smartpoultry/backup:latest \
  --env="MONGO_HOST=mongodb-service" \
  --env="MONGO_DB_NAME=smartpoultry_test" \
  --namespace=smartpoultry-dr-test \
  -- /scripts/restore-production.sh --s3 ${BACKUP_FILE}

# Verify data integrity
kubectl exec -it deployment/mongodb -n smartpoultry-dr-test -- mongosh smartpoultry_test --eval "
  print('Users:', db.users.countDocuments());
  print('Farms:', db.farms.countDocuments());
  print('Chickens:', db.chickens.countDocuments());
"

# Cleanup
kubectl delete namespace smartpoultry-dr-test
```

## Recovery Metrics and SLAs

### Key Metrics

| Metric | Target | Measurement |
|--------|--------|-------------|
| Mean Time to Detection (MTTD) | < 5 minutes | Time from incident to alert |
| Mean Time to Response (MTTR) | < 15 minutes | Time from alert to response |
| Mean Time to Recovery (MTTR) | < 4 hours | Time from incident to resolution |
| Data Loss | < 1 hour | Maximum data loss in any incident |
| Backup Success Rate | > 99% | Percentage of successful backups |
| Recovery Test Success Rate | > 95% | Percentage of successful DR tests |

### Service Level Agreements

#### Availability Targets
- **Production Environment**: 99.9% uptime
- **Database**: 99.95% availability
- **Backup System**: 99.9% success rate

#### Recovery Commitments
- **Database Recovery**: Within 2 hours
- **Application Recovery**: Within 1 hour
- **Full System Recovery**: Within 4 hours

## Continuous Improvement

### Post-Incident Reviews

After each incident:
1. Conduct blameless post-mortem
2. Document lessons learned
3. Update procedures and runbooks
4. Implement preventive measures

### Regular Reviews

- **Monthly**: Review backup success rates and test results
- **Quarterly**: Update disaster recovery procedures
- **Annually**: Comprehensive DR plan review and update

### Training and Awareness

- **New Team Members**: DR training within first month
- **Existing Team**: Quarterly DR drills and training
- **Management**: Annual DR briefings and simulations

## Contact Information

### Emergency Contacts

- **Primary On-Call**: See PagerDuty rotation
- **Backup On-Call**: See PagerDuty rotation
- **Engineering Manager**: <EMAIL>
- **CTO**: <EMAIL>

### External Vendors

- **Cloud Provider Support**: AWS Enterprise Support
- **Database Support**: MongoDB Enterprise
- **Security Incident Response**: <EMAIL>

### Escalation Matrix

1. **Level 1**: On-call Engineer (0-30 minutes)
2. **Level 2**: Engineering Manager (30-60 minutes)
3. **Level 3**: CTO (60+ minutes)
4. **Level 4**: CEO (Major incidents only)

This disaster recovery plan should be reviewed and tested regularly to ensure its effectiveness and accuracy.
